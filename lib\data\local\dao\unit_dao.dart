import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/data/local/database_constants.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'base_dao.dart';
import '../../../core/utils/app_utils.dart';

class UnitDao extends BaseDao<Unit> {
  @override
  String get tableName => DatabaseConstants.tableUnits;

  @override
  Unit fromMap(Map<String, dynamic> map) => Unit.fromMap(map);

  @override
  Map<String, dynamic> toMap(Unit entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Unit-specific queries
  // ------------------------------------------------------------------

  /// البحث عن الوحدات حسب الاسم
  Future<List<Unit>> searchByName(String searchTerm) async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitName} LIKE ?',
      whereArgs: ['%$searchTerm%'],
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// استرجاع الوحدات مع عدّاد الاستخدام
  Future<List<Map<String, dynamic>>> getUnitsWithUsageCount() async {
    final sql = '''
      SELECT 
        u.${DatabaseConstants.columnUnitId},
        u.${DatabaseConstants.columnUnitName},
        u.${DatabaseConstants.columnUnitSymbol},
        u.${DatabaseConstants.columnUnitFactor},
        u.${DatabaseConstants.columnUnitCreatedAt},
        u.${DatabaseConstants.columnUnitUpdatedAt},
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT pp.${DatabaseConstants.columnProductPriceId}) as price_count
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableProductPrices} pp 
        ON u.${DatabaseConstants.columnUnitId} = pp.${DatabaseConstants.columnProductPriceUnitId} 
       AND pp.${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}, 
               u.${DatabaseConstants.columnUnitName}, 
               u.${DatabaseConstants.columnUnitFactor}, 
               u.${DatabaseConstants.columnUnitCreatedAt}, 
               u.${DatabaseConstants.columnUnitUpdatedAt}
      ORDER BY u.${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// التحقق من استخدام الوحدة في المنتجات
  Future<bool> isUsedByProducts(String unitId) async {
    try {
      final db = await database;

      // التحقق من جدول المنتجات
      final productResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProducts} 
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''', [unitId]);

      if ((productResult.first['count'] as int) > 0) return true;

      // التحقق من جدول أسعار المنتجات
      final priceResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProductPrices} 
        WHERE ${DatabaseConstants.columnProductPriceUnitId} = ? 
          AND ${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      ''', [unitId]);

      return (priceResult.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking if unit is used by products', e);
      return false;
    }
  }

  /// استرجاع الوحدات الأساسية (factor = 1.0)
  Future<List<Unit>> getBaseUnits() async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} = 1.0',
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// استرجاع الوحدات التحويلية (factor ≠ 1.0)
  Future<List<Unit>> getConversionUnits() async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} != 1.0',
      orderBy: DatabaseConstants.columnUnitFactor,
    );
  }

  /// تحويل الكمية بين الوحدات
  double convertQuantity(
      double quantity, double fromFactor, double toFactor) {
    final baseQuantity = quantity * fromFactor;
    return baseQuantity / toFactor;
  }

  /// الحصول على معدل التحويل بين وحدتين
  Future<double?> getConversionRate(String fromUnitId, String toUnitId) async {
    try {
      if (fromUnitId == toUnitId) return 1.0;

      final fromUnit = await findById(fromUnitId);
      final toUnit = await findById(toUnitId);

      if (fromUnit == null || toUnit == null) return null;

      return fromUnit.factor / toUnit.factor;
    } catch (e) {
      AppUtils.logError('Error getting conversion rate', e);
      return null;
    }
  }

  /// التحقق من تفرّد اسم الوحدة
  Future<bool> isNameUnique(String name, {String? excludeUnitId}) async {
    try {
      String where = '${DatabaseConstants.columnUnitName} = ? '
          'AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL';
      List<dynamic> whereArgs = [name];

      if (excludeUnitId != null) {
        where += ' AND ${DatabaseConstants.columnUnitId} != ?';
        whereArgs.add(excludeUnitId);
      }

      final count = await this.count(where: where, whereArgs: whereArgs);
      return count == 0;
    } catch (e) {
      AppUtils.logError('Error checking unit name uniqueness', e);
      return false;
    }
  }

  /// استرجاع إحصاءات الوحدة
  Future<Map<String, dynamic>?> getUnitStatistics(String unitId) async {
    final sql = '''
      SELECT 
        u.${DatabaseConstants.columnUnitId},
        u.${DatabaseConstants.columnUnitName},
        u.${DatabaseConstants.columnUnitFactor},
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT pp.${DatabaseConstants.columnProductPriceId}) as price_count,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) as sale_item_count,
        COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId}) as purchase_item_count,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(pi.${DatabaseConstants.columnPurchaseItemQty}), 0) as total_purchased
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableProductPrices} pp 
        ON u.${DatabaseConstants.columnUnitId} = pp.${DatabaseConstants.columnProductPriceUnitId} 
       AND pp.${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON u.${DatabaseConstants.columnUnitId} = si.${DatabaseConstants.columnSaleItemUnitId}
      LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi 
        ON u.${DatabaseConstants.columnUnitId} = pi.${DatabaseConstants.columnPurchaseItemUnitId}
      WHERE u.${DatabaseConstants.columnUnitId} = ? 
        AND u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}, 
               u.${DatabaseConstants.columnUnitName}, 
               u.${DatabaseConstants.columnUnitFactor}
    ''';

    final result = await rawQuery(sql, [unitId]);
    return result.isNotEmpty ? result.first : null;
  }

  /// استرجاع أكثر الوحدات استخداماً
  Future<List<Map<String, dynamic>>> getMostUsedUnits({int limit = 10}) async {
    final sql = '''
      SELECT 
        u.*,
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) as sale_count,
        COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId}) as purchase_count,
        (
          COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) + 
          COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) + 
          COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId})
        ) as total_usage
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON u.${DatabaseConstants.columnUnitId} = si.${DatabaseConstants.columnSaleItemUnitId}
      LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi 
        ON u.${DatabaseConstants.columnUnitId} = pi.${DatabaseConstants.columnPurchaseItemUnitId}
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY total_usage DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  /// حذف وحدة مع التحقق
  Future<bool> deleteUnitSafely(String unitId) async {
    try {
      final isUsed = await isUsedByProducts(unitId);
      if (isUsed) {
        throw Exception('Cannot delete unit: it is being used by products');
      }

      final db = await database;
      final transactionCount = await db.rawQuery('''
        SELECT COUNT(*) as count FROM (
          SELECT 1 FROM ${DatabaseConstants.tableSaleItems} 
            WHERE ${DatabaseConstants.columnSaleItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tablePurchaseItems} 
            WHERE ${DatabaseConstants.columnPurchaseItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tableStockMovements} 
            WHERE ${DatabaseConstants.columnStockMovementUnitId} = ?
        )
      ''', [unitId, unitId, unitId]);

      if ((transactionCount.first['count'] as int) > 0) {
        throw Exception('Cannot delete unit: it has transaction history');
      }

      return await delete(unitId);
    } catch (e) {
      AppUtils.logError('Error deleting unit safely', e);
      return false;
    }
  }

  /// استرجاع الوحدات للقائمة المنسدلة
  Future<List<Map<String, dynamic>>> getUnitsForSelection() async {
    final sql = '''
      SELECT 
        ${DatabaseConstants.columnUnitId} as id,
        ${DatabaseConstants.columnUnitName} as name,
        ${DatabaseConstants.columnUnitFactor} as factor,
        CASE 
          WHEN ${DatabaseConstants.columnUnitFactor} = 1.0 
            THEN ${DatabaseConstants.columnUnitName}
          ELSE ${DatabaseConstants.columnUnitName} || ' (x' || ${DatabaseConstants.columnUnitFactor} || ')'
        END as display_name
      FROM ${DatabaseConstants.tableUnits}
      WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
      ORDER BY ${DatabaseConstants.columnUnitFactor}, ${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// تحديث عوامل التحويل بالجملة
  Future<bool> bulkUpdateFactors(Map<String, double> factorUpdates) async {
    try {
      final db = await database;
      final batch = db.batch();
      final now = DateTime.now().toIso8601String();

      factorUpdates.forEach((unitId, factor) {
        batch.update(
          tableName,
          {
            DatabaseConstants.columnUnitFactor: factor,
            DatabaseConstants.columnUnitUpdatedAt: now,
            DatabaseConstants.columnUnitIsSynced: 0,
          },
          where: '${DatabaseConstants.columnUnitId} = ? AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL',
          whereArgs: [unitId],
        );
      });

      await batch.commit(noResult: true);
      AppUtils.logInfo('Bulk updated factors for ${factorUpdates.length} units');
      return true;
    } catch (e) {
      AppUtils.logError('Error bulk updating factors', e);
      return false;
    }
  }

  /// جدول تحويل الوحدات
  Future<List<Map<String, dynamic>>> getUnitConversionTable() async {
    final sql = '''
      SELECT 
        u1.${DatabaseConstants.columnUnitId} as from_unit_id,
        u1.${DatabaseConstants.columnUnitName} as from_unit_name,
        u1.${DatabaseConstants.columnUnitFactor} as from_factor,
        u2.${DatabaseConstants.columnUnitId} as to_unit_id,
        u2.${DatabaseConstants.columnUnitName} as to_unit_name,
        u2.${DatabaseConstants.columnUnitFactor} as to_factor,
        (u1.${DatabaseConstants.columnUnitFactor} / u2.${DatabaseConstants.columnUnitFactor}) as conversion_rate
      FROM ${DatabaseConstants.tableUnits} u1
      CROSS JOIN ${DatabaseConstants.tableUnits} u2
      WHERE u1.${DatabaseConstants.columnUnitDeletedAt} IS NULL 
        AND u2.${DatabaseConstants.columnUnitDeletedAt} IS NULL
        AND u1.${DatabaseConstants.columnUnitId} != u2.${DatabaseConstants.columnUnitId}
      ORDER BY u1.${DatabaseConstants.columnUnitName}, u2.${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// التحقق من صحة عامل التحويل
  bool isValidFactor(double factor) {
    return factor > 0 && factor.isFinite;
  }

  /// استرجاع الوحدة الافتراضية
  Future<Unit?> getDefaultUnit() async {
    final units = await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} = 1.0',
      orderBy: DatabaseConstants.columnUnitCreatedAt,
      limit: 1,
    );
    return units.isNotEmpty ? units.first : null;
  }
}