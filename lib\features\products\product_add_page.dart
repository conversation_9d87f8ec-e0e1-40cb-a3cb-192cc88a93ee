import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class ProductAddPage extends ConsumerStatefulWidget {
  const ProductAddPage({super.key});

  @override
  ConsumerState<ProductAddPage> createState() => _ProductAddPageState();
}

class _ProductAddPageState extends ConsumerState<ProductAddPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _minStockController = TextEditingController();
  final _maxStockController = TextEditingController();

  String _selectedCategory = 'عام';
  String _selectedUnit = 'قطعة';
  bool _isActive = true;
  bool _trackStock = true;

  @override
  void dispose() {
    _nameController.dispose();
    _barcodeController.dispose();
    _descriptionController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'إضافة منتج جديد',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Card
              _buildBasicInfoCard(),
              SizedBox(height: 16.h),
              
              // Pricing Information Card
              _buildPricingInfoCard(),
              SizedBox(height: 16.h),
              
              // Stock Information Card
              _buildStockInfoCard(),
              SizedBox(height: 16.h),
              
              // Additional Information Card
              _buildAdditionalInfoCard(),
              SizedBox(height: 24.h),
              
              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Product Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج *',
                hintText: 'أدخل اسم المنتج',
                prefixIcon: Icon(Icons.inventory_outlined),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم المنتج';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Barcode
            TextFormField(
              controller: _barcodeController,
              decoration: InputDecoration(
                labelText: 'الباركود',
                hintText: 'أدخل الباركود أو اتركه فارغاً للتوليد التلقائي',
                prefixIcon: const Icon(Icons.qr_code_outlined),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: () {
                    // TODO: Implement barcode scanner
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('ماسح الباركود قيد التطوير')),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 16.h),
            
            // Category
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'التصنيف',
                prefixIcon: Icon(Icons.category_outlined),
              ),
              items: ['عام', 'مواد غذائية', 'إلكترونيات', 'ملابس', 'أدوات منزلية'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedCategory = newValue!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Unit
            DropdownButtonFormField<String>(
              value: _selectedUnit,
              decoration: const InputDecoration(
                labelText: 'وحدة القياس',
                prefixIcon: Icon(Icons.straighten_outlined),
              ),
              items: ['قطعة', 'كيلو', 'لتر', 'متر', 'علبة', 'كرتون'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedUnit = newValue!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التسعير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            Row(
              children: [
                // Cost Price
                Expanded(
                  child: TextFormField(
                    controller: _costPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'سعر التكلفة *',
                      hintText: '0.00',
                      prefixIcon: Icon(Icons.attach_money_outlined),
                      suffixText: 'ر.س',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال سعر التكلفة';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                
                // Selling Price
                Expanded(
                  child: TextFormField(
                    controller: _sellingPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'سعر البيع *',
                      hintText: '0.00',
                      prefixIcon: Icon(Icons.sell_outlined),
                      suffixText: 'ر.س',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال سعر البيع';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // Profit Margin Display
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(Icons.trending_up, color: AppColors.info, size: 20.r),
                  SizedBox(width: 8.w),
                  Text(
                    'هامش الربح: ${_calculateProfitMargin()}%',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المخزون',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Track Stock Switch
            SwitchListTile(
              title: const Text('تتبع المخزون'),
              subtitle: const Text('تفعيل تتبع كمية المخزون'),
              value: _trackStock,
              onChanged: (bool value) {
                setState(() {
                  _trackStock = value;
                });
              },
              activeColor: AppColors.success,
            ),
            
            if (_trackStock) ...[
              SizedBox(height: 16.h),
              Row(
                children: [
                  // Min Stock
                  Expanded(
                    child: TextFormField(
                      controller: _minStockController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'الحد الأدنى للمخزون',
                        hintText: '0',
                        prefixIcon: Icon(Icons.warning_outlined),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 16.w),
                  
                  // Max Stock
                  Expanded(
                    child: TextFormField(
                      controller: _maxStockController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'الحد الأقصى للمخزون',
                        hintText: '0',
                        prefixIcon: Icon(Icons.inventory_2_outlined),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Description
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج',
                hintText: 'أدخل وصف تفصيلي للمنتج',
                prefixIcon: Icon(Icons.description_outlined),
                alignLabelWithHint: true,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Active Status
            SwitchListTile(
              title: const Text('المنتج نشط'),
              subtitle: const Text('تفعيل أو إلغاء تفعيل المنتج'),
              value: _isActive,
              onChanged: (bool value) {
                setState(() {
                  _isActive = value;
                });
              },
              activeColor: AppColors.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => context.pop(),
            child: const Text('إلغاء'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveProduct,
            child: const Text('حفظ المنتج'),
          ),
        ),
      ],
    );
  }

  String _calculateProfitMargin() {
    final costPrice = double.tryParse(_costPriceController.text) ?? 0;
    final sellingPrice = double.tryParse(_sellingPriceController.text) ?? 0;
    
    if (costPrice == 0) return '0.0';
    
    final margin = ((sellingPrice - costPrice) / costPrice) * 100;
    return margin.toStringAsFixed(1);
  }

  void _saveProduct() {
    if (_formKey.currentState!.validate()) {
      // TODO: Implement save product logic
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حفظ المنتج "${_nameController.text}" بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      context.pop();
    }
  }
}
