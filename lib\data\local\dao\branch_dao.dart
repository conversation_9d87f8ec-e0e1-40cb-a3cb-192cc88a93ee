import 'package:tijari_tech/data/local/database_constants.dart';

import '../../../data/models/branch.dart';
import 'base_dao.dart';

class BranchDao extends BaseDao<Branch> {
  @override
  String get tableName => DatabaseConstants.tableBranches;

  @override
  Branch fromMap(Map<String, dynamic> map) => Branch.fromMap(map);

  @override
  Map<String, dynamic> toMap(Branch entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Branch-specific queries
  // ------------------------------------------------------------------

  /// استرجاع فرع حسب الاسم
  Future<Branch?> findByName(String name) async {
    try {
      final branches = await findWhere(
        where: '${DatabaseConstants.columnBranchName} = ?',
        whereArgs: [name],
        limit: 1,
      );
      return branches.isNotEmpty ? branches.first : null;
    } catch (e) {
      rethrow;
    }
  }

  /// البحث عن الفروع حسب الاسم
  Future<List<Branch>> searchByName(String query) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnBranchName} LIKE ?',
        whereArgs: ['%$query%'],
        orderBy: DatabaseConstants.columnBranchName,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// استرجاع جميع الفروع النشطة
  Future<List<Branch>> getActiveBranches() async {
    try {
      return await findAll(orderBy: DatabaseConstants.columnBranchName);
    } catch (e) {
      rethrow;
    }
  }

  /// التحقق من وجود اسم الفرع
  Future<bool> nameExists(String name, {String? excludeId}) async {
    try {
      String where = '${DatabaseConstants.columnBranchName} = ?';
      List<dynamic> whereArgs = [name];

      if (excludeId != null) {
        where += ' AND ${DatabaseConstants.columnBranchId} != ?';
        whereArgs.add(excludeId);
      }

      final count = await super.count(where: where, whereArgs: whereArgs);
      return count > 0;
    } catch (e) {
      return false;
    }
  }

  /// استرجاع الفرع الافتراضي
  Future<Branch?> getDefaultBranch() async {
    try {
      final branches = await findAll(
        limit: 1,
        orderBy: DatabaseConstants.columnBranchCreatedAt,
      );
      return branches.isNotEmpty ? branches.first : null;
    } catch (e) {
      return null;
    }
  }

  /// استرجاع الفروع التي لديها عنوان
  Future<List<Branch>> getBranchesWithAddress() async {
    try {
      return await findWhere(
        where:
            '${DatabaseConstants.columnBranchAddress} IS NOT NULL AND ${DatabaseConstants.columnBranchAddress} != ""',
        orderBy: DatabaseConstants.columnBranchName,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// استرجاع الفروع التي ليس لديها عنوان
  Future<List<Branch>> getBranchesWithoutAddress() async {
    try {
      return await findWhere(
        where:
            '${DatabaseConstants.columnBranchAddress} IS NULL OR ${DatabaseConstants.columnBranchAddress} = ""',
        orderBy: DatabaseConstants.columnBranchName,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// استرجاع إحصاءات الفروع
  Future<Map<String, dynamic>> getBranchStats() async {
    try {
      final totalCount = await count();
      final withAddressCount = await count(
        where:
            '${DatabaseConstants.columnBranchAddress} IS NOT NULL AND ${DatabaseConstants.columnBranchAddress} != ""',
      );
      final withoutAddressCount = totalCount - withAddressCount;

      return {
        'total': totalCount,
        'with_address': withAddressCount,
        'without_address': withoutAddressCount,
      };
    } catch (e) {
      return {
        'total': 0,
        'with_address': 0,
        'without_address': 0,
      };
    }
  }

  /// تحديث عنوان الفرع
  Future<bool> updateAddress(String id, String? address) async {
    try {
      final branch = await findById(id);
      if (branch == null) return false;

      final updatedBranch = branch.update(address: address);
      return await update(id, updatedBranch);
    } catch (e) {
      return false;
    }
  }

  /// تحديث بالجملة
  Future<bool> bulkUpdate(List<Branch> branches) async {
    try {
      final Map<String, Branch> branchMap = {};
      for (final branch in branches) {
        branchMap[branch.id] = branch;
      }
      return await updateBatch(branchMap);
    } catch (e) {
      return false;
    }
  }

  /// استرجاع الفروع المضافة حديثاً
  Future<List<Branch>> getRecentBranches({int limit = 10}) async {
    try {
      return await findAll(
        orderBy: DatabaseConstants.columnBranchCreatedAt,
        ascending: false,
        limit: limit,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// استرجاع الفروع المحدثة حديثاً
  Future<List<Branch>> getRecentlyUpdatedBranches({int limit = 10}) async {
    try {
      return await findAll(
        orderBy: DatabaseConstants.columnBranchUpdatedAt,
        ascending: false,
        limit: limit,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// التحقق من وجود أي فروع
  Future<bool> hasBranches() async {
    try {
      final count = await this.count();
      return count > 0;
    } catch (e) {
      return false;
    }
  }

  /// استرجاع الفروع للقائمة المنسدلة
  Future<List<Map<String, dynamic>>> getBranchesForSelection() async {
    try {
      final branches = await getActiveBranches();
      return branches.map((branch) => {
            'id': branch.id,
            'name': branch.name,
            'address': branch.address ?? '',
          }).toList();
    } catch (e) {
      return [];
    }
  }

  /// التحقق من صحة بيانات الفرع
  bool validateBranch(Branch branch) {
    if (branch.name.trim().isEmpty) {
      return false;
    }

    if (branch.name.length > 100) {
      return false;
    }

    if (branch.address != null && branch.address!.length > 500) {
      return false;
    }

    return true;
  }

  /// إنشاء فرع مع التحقق
  Future<String?> createBranch({
    required String name,
    String? address,
  }) async {
    try {
      if (await nameExists(name)) {
        throw Exception('اسم الفرع موجود مسبقاً');
      }

      final branch = Branch.create(
        id: '',
        name: name.trim(),
        address: address?.trim(),
      );

      if (!validateBranch(branch)) {
        throw Exception('بيانات الفرع غير صحيحة');
      }

      return await insert(branch);
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث فرع مع التحقق
  Future<bool> updateBranch({
    required String id,
    String? name,
    String? address,
  }) async {
    try {
      final existingBranch = await findById(id);
      if (existingBranch == null) {
        throw Exception('الفرع غير موجود');
      }

      final newName = name?.trim() ?? existingBranch.name;

      if (newName != existingBranch.name && await nameExists(newName, excludeId: id)) {
        throw Exception('اسم الفرع موجود مسبقاً');
      }

      final updatedBranch = existingBranch.update(
        name: newName,
        address: address?.trim(),
      );

      if (!validateBranch(updatedBranch)) {
        throw Exception('بيانات الفرع غير صحيحة');
      }

      return await update(id, updatedBranch);
    } catch (e) {
      rethrow;
    }
  }
}