import 'package:flutter_test/flutter_test.dart';
import 'package:tijari_tech/core/utils/app_utils.dart';

void main() {
  group('AppUtils Tests', () {
    group('generateId', () {
      test('should generate unique IDs', () {
        final id1 = AppUtils.generateId();
        final id2 = AppUtils.generateId();
        
        expect(id1, isNotEmpty);
        expect(id2, isNotEmpty);
        expect(id1, isNot(equals(id2)));
      });

      test('should generate valid UUID format', () {
        final id = AppUtils.generateId();
        
        // UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
        final uuidRegex = RegExp(
          r'^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
          caseSensitive: false,
        );
        
        expect(uuidRegex.hasMatch(id), isTrue);
      });
    });

    group('generateInvoiceNumber', () {
      test('should generate invoice number with prefix', () {
        const prefix = 'INV';
        final invoiceNo = AppUtils.generateInvoiceNumber(prefix);
        
        expect(invoiceNo, startsWith(prefix));
        expect(invoiceNo.length, equals(prefix.length + 8));
      });

      test('should generate different invoice numbers', () {
        const prefix = 'INV';
        final invoiceNo1 = AppUtils.generateInvoiceNumber(prefix);
        final invoiceNo2 = AppUtils.generateInvoiceNumber(prefix);
        
        expect(invoiceNo1, isNot(equals(invoiceNo2)));
      });
    });

    group('String utilities', () {
      test('isNullOrEmpty should work correctly', () {
        expect(AppUtils.isNullOrEmpty(null), isTrue);
        expect(AppUtils.isNullOrEmpty(''), isTrue);
        expect(AppUtils.isNullOrEmpty('   '), isTrue);
        expect(AppUtils.isNullOrEmpty('test'), isFalse);
        expect(AppUtils.isNullOrEmpty(' test '), isFalse);
      });

      test('safeString should convert values correctly', () {
        expect(AppUtils.safeString(null), equals(''));
        expect(AppUtils.safeString('test'), equals('test'));
        expect(AppUtils.safeString(123), equals('123'));
        expect(AppUtils.safeString(true), equals('true'));
      });
    });

    group('Number utilities', () {
      test('safeDouble should convert values correctly', () {
        expect(AppUtils.safeDouble(null), equals(0.0));
        expect(AppUtils.safeDouble(123.45), equals(123.45));
        expect(AppUtils.safeDouble(123), equals(123.0));
        expect(AppUtils.safeDouble('123.45'), equals(123.45));
        expect(AppUtils.safeDouble('invalid'), equals(0.0));
      });

      test('safeInt should convert values correctly', () {
        expect(AppUtils.safeInt(null), equals(0));
        expect(AppUtils.safeInt(123), equals(123));
        expect(AppUtils.safeInt(123.45), equals(123));
        expect(AppUtils.safeInt('123'), equals(123));
        expect(AppUtils.safeInt('invalid'), equals(0));
      });

      test('safeBool should convert values correctly', () {
        expect(AppUtils.safeBool(null), isFalse);
        expect(AppUtils.safeBool(true), isTrue);
        expect(AppUtils.safeBool(false), isFalse);
        expect(AppUtils.safeBool(1), isTrue);
        expect(AppUtils.safeBool(0), isFalse);
        expect(AppUtils.safeBool('true'), isTrue);
        expect(AppUtils.safeBool('false'), isFalse);
        expect(AppUtils.safeBool('1'), isTrue);
        expect(AppUtils.safeBool('yes'), isTrue);
        expect(AppUtils.safeBool('no'), isFalse);
      });
    });

    group('Calculation utilities', () {
      test('calculatePercentage should work correctly', () {
        expect(AppUtils.calculatePercentage(25, 100), equals(25.0));
        expect(AppUtils.calculatePercentage(50, 200), equals(25.0));
        expect(AppUtils.calculatePercentage(0, 100), equals(0.0));
        expect(AppUtils.calculatePercentage(100, 0), equals(0.0));
      });

      test('calculatePercentageValue should work correctly', () {
        expect(AppUtils.calculatePercentageValue(100, 25), equals(25.0));
        expect(AppUtils.calculatePercentageValue(200, 50), equals(100.0));
        expect(AppUtils.calculatePercentageValue(0, 25), equals(0.0));
        expect(AppUtils.calculatePercentageValue(100, 0), equals(0.0));
      });

      test('roundToDecimalPlaces should work correctly', () {
        expect(AppUtils.roundToDecimalPlaces(123.456789, 2), equals(123.46));
        expect(AppUtils.roundToDecimalPlaces(123.456789, 0), equals(123.0));
        expect(AppUtils.roundToDecimalPlaces(123.456789, 4), equals(123.4568));
      });
    });

    group('Validation utilities', () {
      test('isValidEmail should work correctly', () {
        expect(AppUtils.isValidEmail('<EMAIL>'), isTrue);
        expect(AppUtils.isValidEmail('<EMAIL>'), isTrue);
        expect(AppUtils.isValidEmail('invalid-email'), isFalse);
        expect(AppUtils.isValidEmail('test@'), isFalse);
        expect(AppUtils.isValidEmail('@example.com'), isFalse);
        expect(AppUtils.isValidEmail(''), isFalse);
      });

      test('isValidPhone should work correctly', () {
        expect(AppUtils.isValidPhone('0501234567'), isTrue);
        expect(AppUtils.isValidPhone('966501234567'), isTrue);
        expect(AppUtils.isValidPhone('+966501234567'), isTrue);
        expect(AppUtils.isValidPhone('123'), isFalse);
        expect(AppUtils.isValidPhone('invalid'), isFalse);
        expect(AppUtils.isValidPhone(''), isFalse);
      });

      test('isValidBarcode should work correctly', () {
        expect(AppUtils.isValidBarcode('1234567890123'), isTrue);
        expect(AppUtils.isValidBarcode('12345678'), isTrue);
        expect(AppUtils.isValidBarcode('123'), isFalse);
        expect(AppUtils.isValidBarcode('12345678901234'), isFalse);
        expect(AppUtils.isValidBarcode('invalid'), isFalse);
        expect(AppUtils.isValidBarcode(''), isFalse);
      });
    });

    group('Number conversion utilities', () {
      test('arabicToEnglishNumbers should work correctly', () {
        expect(AppUtils.arabicToEnglishNumbers('١٢٣٤٥'), equals('12345'));
        expect(AppUtils.arabicToEnglishNumbers('٠٩٨٧٦'), equals('09876'));
        expect(AppUtils.arabicToEnglishNumbers('test١٢٣'), equals('test123'));
        expect(AppUtils.arabicToEnglishNumbers('12345'), equals('12345'));
        expect(AppUtils.arabicToEnglishNumbers(''), equals(''));
      });

      test('englishToArabicNumbers should work correctly', () {
        expect(AppUtils.englishToArabicNumbers('12345'), equals('١٢٣٤٥'));
        expect(AppUtils.englishToArabicNumbers('09876'), equals('٠٩٨٧٦'));
        expect(AppUtils.englishToArabicNumbers('test123'), equals('test١٢٣'));
        expect(AppUtils.englishToArabicNumbers('١٢٣٤٥'), equals('١٢٣٤٥'));
        expect(AppUtils.englishToArabicNumbers(''), equals(''));
      });
    });

    group('Phone number utilities', () {
      test('cleanPhoneNumber should work correctly', () {
        expect(AppUtils.cleanPhoneNumber('0501234567'), equals('966501234567'));
        expect(AppUtils.cleanPhoneNumber('501234567'), equals('966501234567'));
        expect(AppUtils.cleanPhoneNumber('+966501234567'), equals('966501234567'));
        expect(AppUtils.cleanPhoneNumber('966501234567'), equals('966501234567'));
        expect(AppUtils.cleanPhoneNumber('************'), equals('966501234567'));
      });

      test('formatPhoneForDisplay should work correctly', () {
        expect(
          AppUtils.formatPhoneForDisplay('966501234567'),
          equals('+966 5 01 234 567'),
        );
        expect(
          AppUtils.formatPhoneForDisplay('0501234567'),
          equals('+966 5 01 234 567'),
        );
        expect(
          AppUtils.formatPhoneForDisplay('invalid'),
          equals('invalid'),
        );
      });
    });

    group('File size formatting', () {
      test('formatFileSize should work correctly', () {
        expect(AppUtils.formatFileSize(500), equals('500 B'));
        expect(AppUtils.formatFileSize(1024), equals('1.0 KB'));
        expect(AppUtils.formatFileSize(1536), equals('1.5 KB'));
        expect(AppUtils.formatFileSize(1048576), equals('1.0 MB'));
        expect(AppUtils.formatFileSize(1073741824), equals('1.0 GB'));
      });
    });
  });
}
