import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../core/theme/colors.dart';
import '../core/theme/text_styles.dart';
import '../core/utils/extensions.dart';
import '../router/routes.dart';
import 'responsive_wrapper.dart';
import 'notification_widget.dart';
import 'quick_menu_widget.dart';

class MainLayout extends ConsumerWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showBottomNavigation;
  final bool showDrawer;

  const MainLayout({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.floatingActionButton,
    this.showBottomNavigation = true,
    this.showDrawer = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: _buildAppBar(context),
      drawer: showDrawer ? _buildDrawer(context) : null,
      body: ResponsiveWrapper(child: child),
      bottomNavigationBar: showBottomNavigation && context.isMobile
          ? _buildBottomNavigation(context)
          : null,
      floatingActionButton: floatingActionButton,
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
        title ?? AppRoutes.getRouteTitle(GoRouterState.of(context).uri.path),
        style: AppTextStyles.appBarTitle,
      ),
      actions: actions ??
          [
            IconButton(
              icon: const Icon(Icons.sync),
              onPressed: () {
                // TODO: Implement sync functionality
                context.showInfoSnackBar('جاري المزامنة...');
              },
            ),
            const NotificationIcon(),
            IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () {
                _showQuickMenu(context);
              },
            ),
            IconButton(
              icon: const Icon(Icons.settings_outlined),
              onPressed: () => context.go(AppRoutes.settings),
            ),
          ],
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.dashboard_outlined,
                  title: 'لوحة التحكم',
                  route: AppRoutes.dashboard,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.inventory_2_outlined,
                  title: 'المخزون',
                  route: AppRoutes.inventory,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.point_of_sale,
                  title: 'المبيعات',
                  route: AppRoutes.sales,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.shopping_cart_outlined,
                  title: 'المشتريات',
                  route: AppRoutes.purchases,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.people_outline,
                  title: 'الحسابات',
                  route: AppRoutes.accounts,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'الصناديق',
                  route: AppRoutes.cash,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.group_outlined,
                  title: 'الموظفين',
                  route: AppRoutes.employees,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.analytics_outlined,
                  title: 'التقارير',
                  route: AppRoutes.reports,
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings_outlined,
                  title: 'الإعدادات',
                  route: AppRoutes.settings,
                ),
              ],
            ),
          ),
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      height: 200.h,
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradient,
      ),
      child: DrawerHeader(
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 25.r,
              backgroundColor: Colors.white,
              child: Icon(
                Icons.business,
                size: 25.r,
                color: AppColors.primary,
              ),
            ),
            SizedBox(height: 7.h),
            Text(
              'تجاري تك',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'نظام إدارة شامل',
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
  }) {
    final currentRoute = GoRouterState.of(context).uri.path;
    final isSelected = currentRoute.startsWith(route);

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : null,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          color: isSelected ? AppColors.primary : null,
          fontWeight: isSelected ? FontWeight.w600 : null,
        ),
      ),
      selected: isSelected,
      selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
      onTap: () {
        Navigator.of(context).pop();
        context.go(route);
      },
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          const Divider(),
          SizedBox(height: 8.h),
          Text(
            'الإصدار 1.0.0',
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    final currentRoute = GoRouterState.of(context).uri.path;

    int currentIndex = 0;
    if (currentRoute.startsWith(AppRoutes.inventory)) {
      currentIndex = 1;
    } else if (currentRoute.startsWith(AppRoutes.sales)) {
      currentIndex = 2;
    } else if (currentRoute.startsWith(AppRoutes.purchases)) {
      currentIndex = 3;
    } else if (currentRoute.startsWith(AppRoutes.reports)) {
      currentIndex = 4;
    }

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: (index) {
        switch (index) {
          case 0:
            context.go(AppRoutes.dashboard);
            break;
          case 1:
            context.go(AppRoutes.inventory);
            break;
          case 2:
            context.go(AppRoutes.sales);
            break;
          case 3:
            context.go(AppRoutes.purchases);
            break;
          case 4:
            context.go(AppRoutes.reports);
            break;
        }
      },
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard_outlined),
          activeIcon: const Icon(Icons.dashboard),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.inventory_2_outlined),
          activeIcon: const Icon(Icons.inventory_2),
          label: 'المخزون',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.point_of_sale),
          activeIcon: const Icon(Icons.point_of_sale),
          label: 'المبيعات',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.shopping_cart_outlined),
          activeIcon: const Icon(Icons.shopping_cart),
          label: 'المشتريات',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.analytics_outlined),
          activeIcon: const Icon(Icons.analytics),
          label: 'التقارير',
        ),
      ],
    );
  }

  void _showQuickMenu(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600.w,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: const QuickMenuWidget(),
        ),
      ),
    );
  }
}
