import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'category.g.dart';

@JsonSerializable()
class Category {
  final String id;
  final String nameAr;
  final String? nameEn;
  final String? parentId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Category({
    required this.id,
    required this.nameAr,
    this.nameEn,
    this.parentId,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new Category instance from a map
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map[DatabaseConstants.columnCategoryId] as String,
      nameAr: map[DatabaseConstants.columnCategoryNameAr] as String,
      nameEn: map[DatabaseConstants.columnCategoryNameEn] as String?,
      parentId: map[DatabaseConstants.columnCategoryParentId] as String?,
      createdAt: map[DatabaseConstants.columnCategoryCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCategoryCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnCategoryUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCategoryUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnCategoryDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCategoryDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnCategoryIsSynced] as int? ?? 0) == 1,
    );
  }

  // Convert Category instance to a map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnCategoryId: id,
      DatabaseConstants.columnCategoryNameAr: nameAr,
      DatabaseConstants.columnCategoryNameEn: nameEn,
      DatabaseConstants.columnCategoryParentId: parentId,
      DatabaseConstants.columnCategoryCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnCategoryUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnCategoryDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnCategoryIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  // Copy with method for creating modified copies
  Category copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? parentId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Category(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      parentId: parentId ?? this.parentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Category(id: $id, nameAr: $nameAr, nameEn: $nameEn, parentId: $parentId, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasParent => parentId != null && parentId!.isNotEmpty;
  bool get isMainCategory => !hasParent;

  // Get display name based on locale
  String getDisplayName([String locale = 'ar']) {
    if (locale == 'en' && nameEn != null && nameEn!.isNotEmpty) {
      return nameEn!;
    }
    return nameAr;
  }

  // Create a new category with current timestamp
  static Category create({
    required String id,
    required String nameAr,
    String? nameEn,
    String? parentId,
  }) {
    final now = DateTime.now();
    return Category(
      id: id,
      nameAr: nameAr,
      nameEn: nameEn,
      parentId: parentId,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update category with new timestamp
  Category update({
    String? nameAr,
    String? nameEn,
    String? parentId,
  }) {
    return copyWith(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      parentId: parentId ?? this.parentId,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Category markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Category markAsSynced() {
    return copyWith(isSynced: true);
  }
}
