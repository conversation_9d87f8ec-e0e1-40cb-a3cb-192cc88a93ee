import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class SalesReturnPage extends ConsumerStatefulWidget {
  const SalesReturnPage({super.key});

  @override
  ConsumerState<SalesReturnPage> createState() => _SalesReturnPageState();
}

class _SalesReturnPageState extends ConsumerState<SalesReturnPage> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _customerSearchController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  String _selectedReason = 'عيب في المنتج';
  
  Map<String, dynamic>? _selectedInvoice;
  final List<Map<String, dynamic>> _returnItems = [];

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _customerSearchController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'مرتجعات المبيعات',
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header Section
            _buildHeaderSection(),
            
            // Invoice Selection
            if (_selectedInvoice == null) _buildInvoiceSelection(),
            
            // Invoice Details & Items
            if (_selectedInvoice != null)
              Expanded(
                child: Column(
                  children: [
                    _buildInvoiceDetails(),
                    Expanded(child: _buildItemsSection()),
                  ],
                ),
              ),
            
            // Action Buttons
            if (_selectedInvoice != null) _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Row(
        children: [
          // Return Date
          Expanded(
            child: InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ المرتجع',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          
          // Return Reason
          Expanded(
            flex: 2,
            child: DropdownButtonFormField<String>(
              value: _selectedReason,
              decoration: const InputDecoration(
                labelText: 'سبب المرتجع',
                prefixIcon: Icon(Icons.info_outline),
              ),
              items: [
                'عيب في المنتج',
                'منتج خاطئ',
                'تلف أثناء الشحن',
                'عدم رضا العميل',
                'انتهاء صلاحية',
                'أخرى'
              ].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedReason = newValue!;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceSelection() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            // Invoice Number Search
            TextFormField(
              controller: _invoiceNumberController,
              decoration: InputDecoration(
                labelText: 'رقم الفاتورة',
                hintText: 'أدخل رقم الفاتورة',
                prefixIcon: const Icon(Icons.receipt_outlined),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _searchInvoice,
                ),
              ),
              onFieldSubmitted: (_) => _searchInvoice(),
            ),
            SizedBox(height: 24.h),
            
            // Customer Search
            TextFormField(
              controller: _customerSearchController,
              decoration: InputDecoration(
                labelText: 'البحث بالعميل',
                hintText: 'اسم العميل أو رقم الهاتف',
                prefixIcon: const Icon(Icons.person_search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _searchByCustomer,
                ),
              ),
            ),
            SizedBox(height: 32.h),
            
            // Recent Invoices
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الفواتير الأخيرة',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Expanded(
                    child: _buildRecentInvoicesList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentInvoicesList() {
    final recentInvoices = _getRecentInvoices();
    
    return ListView.builder(
      itemCount: recentInvoices.length,
      itemBuilder: (context, index) {
        final invoice = recentInvoices[index];
        return Card(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: Icon(
                Icons.receipt_outlined,
                color: AppColors.primary,
              ),
            ),
            title: Text(
              'فاتورة #${invoice['number']}',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('العميل: ${invoice['customer']}'),
                Text('التاريخ: ${invoice['date']}'),
                Text('المبلغ: ${invoice['total']} ر.س'),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _selectInvoice(invoice),
          ),
        );
      },
    );
  }

  Widget _buildInvoiceDetails() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.blue[50],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'فاتورة #${_selectedInvoice!['number']}',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedInvoice = null;
                    _returnItems.clear();
                  });
                },
                child: const Text('تغيير الفاتورة'),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: Text('العميل: ${_selectedInvoice!['customer']}'),
              ),
              Expanded(
                child: Text('التاريخ: ${_selectedInvoice!['date']}'),
              ),
              Expanded(
                child: Text('المبلغ: ${_selectedInvoice!['total']} ر.س'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSection() {
    final invoiceItems = _selectedInvoice!['items'] as List<Map<String, dynamic>>;
    
    return Column(
      children: [
        // Items Header
        Container(
          padding: EdgeInsets.all(16.r),
          color: AppColors.primary,
          child: Row(
            children: [
              Expanded(flex: 3, child: Text('الصنف', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold))),
              Expanded(flex: 1, child: Text('الكمية', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold), textAlign: TextAlign.center)),
              Expanded(flex: 1, child: Text('السعر', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold), textAlign: TextAlign.center)),
              Expanded(flex: 1, child: Text('مرتجع', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold), textAlign: TextAlign.center)),
            ],
          ),
        ),
        
        // Items List
        Expanded(
          child: ListView.builder(
            itemCount: invoiceItems.length,
            itemBuilder: (context, index) {
              final item = invoiceItems[index];
              final returnItem = _returnItems.firstWhere(
                (r) => r['itemId'] == item['id'],
                orElse: () => {'returnQty': 0},
              );
              
              return Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item['name'],
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'الباركود: ${item['barcode']}',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        item['qty'].toString(),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${item['price']} ر.س',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: SizedBox(
                        width: 60.w,
                        child: TextFormField(
                          initialValue: returnItem['returnQty'].toString(),
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          ),
                          onChanged: (value) => _updateReturnQuantity(item['id'], value),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        
        // Return Summary
        _buildReturnSummary(),
      ],
    );
  }

  Widget _buildReturnSummary() {
    final totalReturnAmount = _calculateTotalReturnAmount();
    final totalReturnItems = _returnItems.where((item) => item['returnQty'] > 0).length;
    
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[100],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('عدد الأصناف المرتجعة: $totalReturnItems'),
              Text(
                'إجمالي المرتجع: ${totalReturnAmount.toStringAsFixed(2)} ر.س',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.error,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // Notes
          TextFormField(
            controller: _notesController,
            maxLines: 2,
            decoration: const InputDecoration(
              labelText: 'ملاحظات المرتجع',
              hintText: 'أدخل ملاحظات إضافية...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final hasReturnItems = _returnItems.any((item) => item['returnQty'] > 0);
    
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              child: const Text('إلغاء'),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: ElevatedButton(
              onPressed: hasReturnItems ? _processReturn : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
              ),
              child: const Text('تنفيذ المرتجع'),
            ),
          ),
        ],
      ),
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _searchInvoice() {
    // TODO: Search invoice by number
    final invoiceNumber = _invoiceNumberController.text;
    if (invoiceNumber.isNotEmpty) {
      // Mock search result
      final invoice = _getRecentInvoices().firstWhere(
        (inv) => inv['number'].toString().contains(invoiceNumber),
        orElse: () => {},
      );
      
      if (invoice.isNotEmpty) {
        _selectInvoice(invoice);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لم يتم العثور على الفاتورة')),
        );
      }
    }
  }

  void _searchByCustomer() {
    // TODO: Search invoices by customer
  }

  void _selectInvoice(Map<String, dynamic> invoice) {
    setState(() {
      _selectedInvoice = invoice;
      _returnItems.clear();
      
      // Initialize return items
      for (var item in invoice['items']) {
        _returnItems.add({
          'itemId': item['id'],
          'returnQty': 0,
          'maxQty': item['qty'],
          'price': item['price'],
        });
      }
    });
  }

  void _updateReturnQuantity(String itemId, String value) {
    final qty = int.tryParse(value) ?? 0;
    final returnItemIndex = _returnItems.indexWhere((item) => item['itemId'] == itemId);
    
    if (returnItemIndex != -1) {
      final maxQty = _returnItems[returnItemIndex]['maxQty'];
      setState(() {
        _returnItems[returnItemIndex]['returnQty'] = qty > maxQty ? maxQty : qty;
      });
    }
  }

  double _calculateTotalReturnAmount() {
    double total = 0;
    for (var item in _returnItems) {
      total += (item['returnQty'] * item['price']);
    }
    return total;
  }

  void _processReturn() {
    if (_formKey.currentState!.validate()) {
      // TODO: Process return in database
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تنفيذ المرتجع بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      context.pop();
    }
  }

  List<Map<String, dynamic>> _getRecentInvoices() {
    return [
      {
        'id': '1',
        'number': 'INV-001',
        'customer': 'أحمد محمد',
        'date': '2024/01/15',
        'total': '1,250',
        'items': [
          {'id': '1', 'name': 'منتج أ', 'barcode': '123456', 'qty': 2, 'price': 50.0},
          {'id': '2', 'name': 'منتج ب', 'barcode': '789012', 'qty': 1, 'price': 150.0},
        ],
      },
      {
        'id': '2',
        'number': 'INV-002',
        'customer': 'فاطمة أحمد',
        'date': '2024/01/14',
        'total': '850',
        'items': [
          {'id': '3', 'name': 'منتج ج', 'barcode': '345678', 'qty': 3, 'price': 75.0},
        ],
      },
    ];
  }
}
