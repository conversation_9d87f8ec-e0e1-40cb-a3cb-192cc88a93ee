import 'package:json_annotation/json_annotation.dart';

part 'stock_adjustment.g.dart';

@JsonSerializable()
class StockAdjustment {
  final String id;
  final String referenceNumber;
  final DateTime adjustmentDate;
  final String warehouseId;
  final String reason;
  final String? notes;
  final String status; // draft, completed, cancelled
  final double totalValue;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? branchId;
  final List<StockAdjustmentItem> items;

  const StockAdjustment({
    required this.id,
    required this.referenceNumber,
    required this.adjustmentDate,
    required this.warehouseId,
    required this.reason,
    this.notes,
    this.status = 'draft',
    this.totalValue = 0.0,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.branchId,
    this.items = const [],
  });

  factory StockAdjustment.fromJson(Map<String, dynamic> json) => _$StockAdjustmentFromJson(json);
  Map<String, dynamic> toJson() => _$StockAdjustmentToJson(this);

  factory StockAdjustment.fromMap(Map<String, dynamic> map) {
    return StockAdjustment(
      id: map['id'] as String,
      referenceNumber: map['reference_number'] as String,
      adjustmentDate: DateTime.parse(map['adjustment_date'] as String),
      warehouseId: map['warehouse_id'] as String,
      reason: map['reason'] as String,
      notes: map['notes'] as String?,
      status: map['status'] as String? ?? 'draft',
      totalValue: (map['total_value'] as num?)?.toDouble() ?? 0.0,
      createdBy: map['created_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String) 
          : null,
      branchId: map['branch_id'] as String?,
      items: [], // Items loaded separately
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference_number': referenceNumber,
      'adjustment_date': adjustmentDate.toIso8601String(),
      'warehouse_id': warehouseId,
      'reason': reason,
      'notes': notes,
      'status': status,
      'total_value': totalValue,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'branch_id': branchId,
    };
  }

  StockAdjustment copyWith({
    String? id,
    String? referenceNumber,
    DateTime? adjustmentDate,
    String? warehouseId,
    String? reason,
    String? notes,
    String? status,
    double? totalValue,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? branchId,
    List<StockAdjustmentItem>? items,
  }) {
    return StockAdjustment(
      id: id ?? this.id,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      adjustmentDate: adjustmentDate ?? this.adjustmentDate,
      warehouseId: warehouseId ?? this.warehouseId,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      totalValue: totalValue ?? this.totalValue,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      branchId: branchId ?? this.branchId,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAdjustment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAdjustment(id: $id, referenceNumber: $referenceNumber, reason: $reason)';
  }

  // Helper methods
  bool get isDraft => status == StockAdjustmentStatus.draft;
  bool get isCompleted => status == StockAdjustmentStatus.completed;
  bool get isCancelled => status == StockAdjustmentStatus.cancelled;

  int get totalItems => items.length;
  int get increaseItems => items.where((item) => item.adjustmentType == 'increase').length;
  int get decreaseItems => items.where((item) => item.adjustmentType == 'decrease').length;

  String get displayReason {
    switch (reason) {
      case StockAdjustmentReason.damaged:
        return 'تالف';
      case StockAdjustmentReason.lost:
        return 'فاقد';
      case StockAdjustmentReason.expired:
        return 'منتهي الصلاحية';
      case StockAdjustmentReason.gift:
        return 'هدية';
      case StockAdjustmentReason.sample:
        return 'عينة';
      case StockAdjustmentReason.countingError:
        return 'خطأ جرد';
      case StockAdjustmentReason.other:
        return 'أخرى';
      default:
        return reason;
    }
  }

  String get displayStatus {
    switch (status) {
      case StockAdjustmentStatus.draft:
        return 'مسودة';
      case StockAdjustmentStatus.completed:
        return 'مكتمل';
      case StockAdjustmentStatus.cancelled:
        return 'ملغي';
      default:
        return status;
    }
  }
}

@JsonSerializable()
class StockAdjustmentItem {
  final String id;
  final String adjustmentId;
  final String productId;
  final double currentQuantity;
  final double adjustmentQuantity;
  final double newQuantity;
  final String adjustmentType; // increase, decrease
  final double unitCost;
  final double totalValue;
  final String? notes;

  const StockAdjustmentItem({
    required this.id,
    required this.adjustmentId,
    required this.productId,
    required this.currentQuantity,
    required this.adjustmentQuantity,
    required this.newQuantity,
    required this.adjustmentType,
    required this.unitCost,
    required this.totalValue,
    this.notes,
  });

  factory StockAdjustmentItem.fromJson(Map<String, dynamic> json) => _$StockAdjustmentItemFromJson(json);
  Map<String, dynamic> toJson() => _$StockAdjustmentItemToJson(this);

  factory StockAdjustmentItem.fromMap(Map<String, dynamic> map) {
    return StockAdjustmentItem(
      id: map['id'] as String,
      adjustmentId: map['adjustment_id'] as String,
      productId: map['product_id'] as String,
      currentQuantity: (map['current_quantity'] as num).toDouble(),
      adjustmentQuantity: (map['adjustment_quantity'] as num).toDouble(),
      newQuantity: (map['new_quantity'] as num).toDouble(),
      adjustmentType: map['adjustment_type'] as String,
      unitCost: (map['unit_cost'] as num).toDouble(),
      totalValue: (map['total_value'] as num).toDouble(),
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'adjustment_id': adjustmentId,
      'product_id': productId,
      'current_quantity': currentQuantity,
      'adjustment_quantity': adjustmentQuantity,
      'new_quantity': newQuantity,
      'adjustment_type': adjustmentType,
      'unit_cost': unitCost,
      'total_value': totalValue,
      'notes': notes,
    };
  }

  StockAdjustmentItem copyWith({
    String? id,
    String? adjustmentId,
    String? productId,
    double? currentQuantity,
    double? adjustmentQuantity,
    double? newQuantity,
    String? adjustmentType,
    double? unitCost,
    double? totalValue,
    String? notes,
  }) {
    return StockAdjustmentItem(
      id: id ?? this.id,
      adjustmentId: adjustmentId ?? this.adjustmentId,
      productId: productId ?? this.productId,
      currentQuantity: currentQuantity ?? this.currentQuantity,
      adjustmentQuantity: adjustmentQuantity ?? this.adjustmentQuantity,
      newQuantity: newQuantity ?? this.newQuantity,
      adjustmentType: adjustmentType ?? this.adjustmentType,
      unitCost: unitCost ?? this.unitCost,
      totalValue: totalValue ?? this.totalValue,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAdjustmentItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAdjustmentItem(id: $id, productId: $productId, adjustmentType: $adjustmentType, adjustmentQuantity: $adjustmentQuantity)';
  }

  // Helper methods
  bool get isIncrease => adjustmentType == 'increase';
  bool get isDecrease => adjustmentType == 'decrease';
}

// Stock adjustment reasons constants
class StockAdjustmentReason {
  static const String damaged = 'damaged';
  static const String lost = 'lost';
  static const String expired = 'expired';
  static const String gift = 'gift';
  static const String sample = 'sample';
  static const String countingError = 'counting_error';
  static const String other = 'other';

  static List<String> get allReasons => [
    damaged, lost, expired, gift, sample, countingError, other,
  ];

  static List<String> get displayNames => [
    'تالف', 'فاقد', 'منتهي الصلاحية', 'هدية', 'عينة', 'خطأ جرد', 'أخرى',
  ];
}

// Stock adjustment status constants
class StockAdjustmentStatus {
  static const String draft = 'draft';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';

  static List<String> get allStatuses => [
    draft, completed, cancelled,
  ];
}
