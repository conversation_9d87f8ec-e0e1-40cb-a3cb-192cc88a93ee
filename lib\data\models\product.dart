import 'package:json_annotation/json_annotation.dart';
import 'package:tijari_tech/data/local/database_constants.dart';

part 'product.g.dart';

@JsonSerializable()
class Product {
  final String id;
  final String nameAr;
  final String? nameEn;
  final String? barcode;
  final String? description;
  final String? categoryId;
  final String? baseUnitId;
  final double costPrice; // ← إضافة
  final double sellingPrice; // ← إضافة
  final double minStock;
  final double maxStock;
  final double reorderPoint;
  final bool trackStock;
  final bool isActive;
  final String? imageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Product({
    required this.id,
    required this.nameAr,
    this.nameEn,
    this.barcode,
    this.description,
    this.categoryId,
    this.baseUnitId,
    required this.costPrice,
    required this.sellingPrice,
    this.minStock = 0,
    this.maxStock = 0,
    this.reorderPoint = 0,
    this.trackStock = true,
    this.isActive = true,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // ------------------------------------------------------------------
  // Map / JSON
  // ------------------------------------------------------------------
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map[DatabaseConstants.columnProductId] as String,
      nameAr: map[DatabaseConstants.columnProductNameAr] as String,
      nameEn: map[DatabaseConstants.columnProductNameEn] as String?,
      barcode: map[DatabaseConstants.columnProductBarcode] as String?,
      description: map[DatabaseConstants.columnProductDescription] as String?,
      categoryId: map[DatabaseConstants.columnProductCategoryId] as String?,
      baseUnitId: map[DatabaseConstants.columnProductBaseUnitId] as String?,
      costPrice:
          (map[DatabaseConstants.columnProductCostPrice] as num).toDouble(),
      sellingPrice:
          (map[DatabaseConstants.columnProductSellingPrice] as num).toDouble(),
      minStock:
          (map[DatabaseConstants.columnProductMinStock] as num?)?.toDouble() ??
              0,
      maxStock:
          (map[DatabaseConstants.columnProductMaxStock] as num?)?.toDouble() ??
              0,
      reorderPoint: (map[DatabaseConstants.columnProductReorderPoint] as num?)
              ?.toDouble() ??
          0,
      trackStock:
          (map[DatabaseConstants.columnProductTrackStock] as int? ?? 1) == 1,
      isActive:
          (map[DatabaseConstants.columnProductIsActive] as int? ?? 1) == 1,
      imageUrl: map[DatabaseConstants.columnProductImageUrl] as String?,
      createdAt: map[DatabaseConstants.columnProductCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnProductUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnProductDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnProductIsSynced] as int? ?? 0) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnProductId: id,
      DatabaseConstants.columnProductNameAr: nameAr,
      DatabaseConstants.columnProductNameEn: nameEn,
      DatabaseConstants.columnProductBarcode: barcode,
      DatabaseConstants.columnProductDescription: description,
      DatabaseConstants.columnProductCategoryId: categoryId,
      DatabaseConstants.columnProductBaseUnitId: baseUnitId,
      DatabaseConstants.columnProductCostPrice: costPrice,
      DatabaseConstants.columnProductSellingPrice: sellingPrice,
      DatabaseConstants.columnProductMinStock: minStock,
      DatabaseConstants.columnProductMaxStock: maxStock,
      DatabaseConstants.columnProductReorderPoint: reorderPoint,
      DatabaseConstants.columnProductTrackStock: trackStock ? 1 : 0,
      DatabaseConstants.columnProductIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnProductImageUrl: imageUrl,
      DatabaseConstants.columnProductCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnProductUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnProductDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnProductIsSynced: isSynced ? 1 : 0,
    };
  }

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  // ------------------------------------------------------------------
  // CopyWith
  // ------------------------------------------------------------------
  Product copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double? costPrice,
    double? sellingPrice,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    bool? trackStock,
    bool? isActive,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Product(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      barcode: barcode ?? this.barcode,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      reorderPoint: reorderPoint ?? this.reorderPoint,
      trackStock: trackStock ?? this.trackStock,
      isActive: isActive ?? this.isActive,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // ------------------------------------------------------------------
  // Derived helpers
  // ------------------------------------------------------------------
  bool get isDeleted => deletedAt != null;
  //bool get isActive => !isDeleted;
  bool get hasBarcode => barcode != null && barcode!.isNotEmpty;
  bool get hasCategory => categoryId != null && categoryId!.isNotEmpty;
  bool get hasBaseUnit => baseUnitId != null && baseUnitId!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  bool get needsReorder => minStock <= reorderPoint;
  double get profit => sellingPrice - costPrice;
  double get profitMargin => costPrice > 0 ? profit / costPrice : 0;

  StockStatus getStockStatus(double currentStock) {
    if (currentStock <= 0) return StockStatus.outOfStock;
    if (currentStock <= reorderPoint) return StockStatus.lowStock;
    return StockStatus.inStock;
  }

  // ------------------------------------------------------------------
  // Factory creators
  // ------------------------------------------------------------------
  static Product create({
    required String id,
    required String nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double costPrice = 0,
    double sellingPrice = 0,
    double minStock = 0,
    double maxStock = 0,
    double reorderPoint = 0,
    String? imageUrl,
  }) {
    final now = DateTime.now();
    return Product(
      id: id,
      nameAr: nameAr,
      nameEn: nameEn,
      barcode: barcode,
      description: description,
      categoryId: categoryId,
      baseUnitId: baseUnitId,
      costPrice: costPrice,
      sellingPrice: sellingPrice,
      minStock: minStock,
      maxStock: maxStock,
      reorderPoint: reorderPoint,
      imageUrl: imageUrl,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  Product markAsDeleted() => copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Product markAsSynced() => copyWith(isSynced: true);
}

enum StockStatus {
  inStock,
  lowStock,
  outOfStock,
}

extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'متوفر';
      case StockStatus.lowStock:
        return 'منخفض';
      case StockStatus.outOfStock:
        return 'نفذ';
    }
  }
}
