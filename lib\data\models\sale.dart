import 'package:json_annotation/json_annotation.dart';
import 'package:tijari_tech/data/local/database_constants.dart';

part 'sale.g.dart';

@JsonSerializable()
class Sale {
  // Core database fields
  final String id;
  final String? customerId;
  final String invoiceNo;
  final String? branchId;
  final DateTime date;
  final double total;
  final double paid;
  final double due;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  // Extra fields you may need in the future (will be ignored in DB for now)
  final double? tax; // not in current table
  final double? discount; // not in current table
  final String? status; // not in current table
  final String? paymentMethod; // not in current table
  final double? shippingCost; // not in current table

  const Sale({
    required this.id,
    this.customerId,
    required this.invoiceNo,
    this.branchId,
    required this.date,
    required this.total,
    required this.paid,
    required this.due,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
    this.tax = 0.0,
    this.discount = 0.0,
    this.status,
    this.paymentMethod,
    this.shippingCost = 0.0,
  });

  // ------------------------------------------------------------------
  // JSON & MAP
  // ------------------------------------------------------------------
  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map[DatabaseConstants.columnSaleId] as String,
      customerId: map[DatabaseConstants.columnSaleCustomerId] as String?,
      invoiceNo: map[DatabaseConstants.columnSaleInvoiceNo] as String,
      branchId: map[DatabaseConstants.columnSaleBranchId] as String?,
      date: DateTime.parse(map[DatabaseConstants.columnSaleSaleDate] as String),
      total: (map[DatabaseConstants.columnSaleTotalAmount] as num).toDouble(),
      paid: (map[DatabaseConstants.columnSalePaidAmount] as num).toDouble(),
      due: (map[DatabaseConstants.columnSaleDueAmount] as num).toDouble(),
      notes: map[DatabaseConstants.columnSaleNotes] as String?,
      createdAt: map[DatabaseConstants.columnSaleCreatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSaleCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnSaleUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSaleUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnSaleDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSaleDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnSaleIsSynced] as int? ?? 0) == 1,
      tax: (map['tax'] as num?)?.toDouble() ?? 0.0,
      discount: (map['discount'] as num?)?.toDouble() ?? 0.0,
      status: map['status'] as String?,
      paymentMethod: map['payment_method'] as String?,
      shippingCost: (map['shipping_cost'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    // Only persist columns that actually exist in the table
    return {
      DatabaseConstants.columnSaleId: id,
      DatabaseConstants.columnSaleCustomerId: customerId,
      DatabaseConstants.columnSaleInvoiceNo: invoiceNo,
      DatabaseConstants.columnSaleBranchId: branchId,
      DatabaseConstants.columnSaleSaleDate: date.toIso8601String(),
      DatabaseConstants.columnSaleTotalAmount: total,
      DatabaseConstants.columnSalePaidAmount: paid,
      DatabaseConstants.columnSaleDueAmount: due,
      DatabaseConstants.columnSaleNotes: notes,
      DatabaseConstants.columnSaleCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnSaleUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnSaleDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnSaleIsSynced: isSynced ? 1 : 0,
      // Extra fields are omitted here; they would be saved in a separate table or extension
    };
  }

  factory Sale.fromJson(Map<String, dynamic> json) => _$SaleFromJson(json);
  Map<String, dynamic> toJson() => _$SaleToJson(this);

  // ------------------------------------------------------------------
  // Helpers
  // ------------------------------------------------------------------
  Sale copyWith({
    String? id,
    String? customerId,
    String? invoiceNo,
    String? branchId,
    DateTime? date,
    double? total,
    double? paid,
    double? due,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
    double? tax,
    double? discount,
    String? status,
    String? paymentMethod,
    double? shippingCost,
  }) {
    return Sale(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      branchId: branchId ?? this.branchId,
      date: date ?? this.date,
      total: total ?? this.total,
      paid: paid ?? this.paid,
      due: due ?? this.due,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      shippingCost: shippingCost ?? this.shippingCost,
    );
  }

  // ------------------------------------------------------------------
  // Derived Getters
  // ------------------------------------------------------------------
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasCustomer => customerId != null && customerId!.isNotEmpty;
  bool get hasBranch => branchId != null && branchId!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;

  bool get isPaid => due <= 0;
  bool get isPartiallyPaid => paid > 0 && due > 0;
  bool get isUnpaid => paid <= 0;
  bool get isOverpaid => paid > total;

  double get subtotal => total - (tax ?? 0) + (discount ?? 0);
  double get grandTotal =>
      total + (tax ?? 0) + (shippingCost ?? 0) - (discount ?? 0);

  PaymentStatus get paymentStatus {
    if (paid >= total) return PaymentStatus.paid;
    if (paid > 0) return PaymentStatus.partiallyPaid;
    return PaymentStatus.unpaid;
  }

  // ------------------------------------------------------------------
  // Factories
  // ------------------------------------------------------------------
  factory Sale.create({
    required String id,
    String? customerId,
    required String invoiceNo,
    String? branchId,
    DateTime? date,
    required double total,
    double paid = 0.0,
    String? notes,
    double? tax,
    double? discount,
    double? shippingCost,
    String? status,
    String? paymentMethod,
  }) {
    final now = DateTime.now();
    final due = total - paid;
    return Sale(
      id: id,
      customerId: customerId,
      invoiceNo: invoiceNo,
      branchId: branchId,
      date: date ?? now,
      total: total,
      paid: paid,
      due: due,
      notes: notes,
      tax: tax,
      discount: discount,
      shippingCost: shippingCost,
      status: status,
      paymentMethod: paymentMethod,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // ------------------------------------------------------------------
  // Business Logic
  // ------------------------------------------------------------------
  Sale addPayment(double amount) {
    final newPaid = paid + amount;
    final newDue = total - newPaid;
    return copyWith(
      paid: newPaid.clamp(0.0, double.infinity),
      due: newDue.clamp(0.0, double.infinity),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  Sale markAsPaid() => copyWith(
        paid: total,
        due: 0.0,
        status: 'paid',
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Sale markAsDeleted() => copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Sale markAsSynced() => copyWith(isSynced: true);
}

enum PaymentStatus {
  paid,
  partiallyPaid,
  unpaid,
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.paid:
        return 'مدفوع';
      case PaymentStatus.partiallyPaid:
        return 'مدفوع جزئياً';
      case PaymentStatus.unpaid:
        return 'غير مدفوع';
    }
  }
}
