// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      nameAr: json['nameAr'] as String,
      nameEn: json['nameEn'] as String?,
      barcode: json['barcode'] as String?,
      description: json['description'] as String?,
      categoryId: json['categoryId'] as String?,
      baseUnitId: json['baseUnitId'] as String?,
      costPrice: (json['costPrice'] as num).toDouble(),
      sellingPrice: (json['sellingPrice'] as num).toDouble(),
      minStock: (json['minStock'] as num?)?.toDouble() ?? 0,
      maxStock: (json['maxStock'] as num?)?.toDouble() ?? 0,
      reorderPoint: (json['reorderPoint'] as num?)?.toDouble() ?? 0,
      trackStock: json['trackStock'] as bool? ?? true,
      isActive: json['isActive'] as bool? ?? true,
      imageUrl: json['imageUrl'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'barcode': instance.barcode,
      'description': instance.description,
      'categoryId': instance.categoryId,
      'baseUnitId': instance.baseUnitId,
      'costPrice': instance.costPrice,
      'sellingPrice': instance.sellingPrice,
      'minStock': instance.minStock,
      'maxStock': instance.maxStock,
      'reorderPoint': instance.reorderPoint,
      'trackStock': instance.trackStock,
      'imageUrl': instance.imageUrl,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'isActive': instance.isActive,
    };
