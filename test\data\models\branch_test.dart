import 'package:flutter_test/flutter_test.dart';
import 'package:tijari_tech/data/models/branch.dart';

void main() {
  group('Branch Model Tests', () {
    group('Constructor', () {
      test('should create branch with required fields', () {
        final branch = Branch(
          id: 'test-id',
          name: 'Test Branch',
        );

        expect(branch.id, equals('test-id'));
        expect(branch.name, equals('Test Branch'));
        expect(branch.address, isNull);
        expect(branch.isSynced, isFalse);
      });

      test('should create branch with all fields', () {
        final now = DateTime.now();
        final branch = Branch(
          id: 'test-id',
          name: 'Test Branch',
          address: 'Test Address',
          createdAt: now,
          updatedAt: now,
          isSynced: true,
        );

        expect(branch.id, equals('test-id'));
        expect(branch.name, equals('Test Branch'));
        expect(branch.address, equals('Test Address'));
        expect(branch.createdAt, equals(now));
        expect(branch.updatedAt, equals(now));
        expect(branch.isSynced, isTrue);
      });
    });

    group('fromMap', () {
      test('should create branch from map', () {
        final map = {
          'id': 'test-id',
          'name': 'Test Branch',
          'address': 'Test Address',
          'created_at': '2023-01-01T00:00:00.000Z',
          'updated_at': '2023-01-01T00:00:00.000Z',
          'is_synced': 1,
        };

        final branch = Branch.fromMap(map);

        expect(branch.id, equals('test-id'));
        expect(branch.name, equals('Test Branch'));
        expect(branch.address, equals('Test Address'));
        expect(branch.createdAt, isNotNull);
        expect(branch.updatedAt, isNotNull);
        expect(branch.isSynced, isTrue);
      });

      test('should handle null values', () {
        final map = {
          'id': 'test-id',
          'name': 'Test Branch',
          'address': null,
          'created_at': null,
          'updated_at': null,
          'is_synced': 0,
        };

        final branch = Branch.fromMap(map);

        expect(branch.id, equals('test-id'));
        expect(branch.name, equals('Test Branch'));
        expect(branch.address, isNull);
        expect(branch.createdAt, isNull);
        expect(branch.updatedAt, isNull);
        expect(branch.isSynced, isFalse);
      });
    });

    group('toMap', () {
      test('should convert branch to map', () {
        final now = DateTime.now();
        final branch = Branch(
          id: 'test-id',
          name: 'Test Branch',
          address: 'Test Address',
          createdAt: now,
          updatedAt: now,
          isSynced: true,
        );

        final map = branch.toMap();

        expect(map['id'], equals('test-id'));
        expect(map['name'], equals('Test Branch'));
        expect(map['address'], equals('Test Address'));
        expect(map['created_at'], equals(now.toIso8601String()));
        expect(map['updated_at'], equals(now.toIso8601String()));
        expect(map['is_synced'], equals(1));
      });

      test('should handle null values in toMap', () {
        final branch = Branch(
          id: 'test-id',
          name: 'Test Branch',
        );

        final map = branch.toMap();

        expect(map['id'], equals('test-id'));
        expect(map['name'], equals('Test Branch'));
        expect(map['address'], isNull);
        expect(map['created_at'], isNull);
        expect(map['updated_at'], isNull);
        expect(map['is_synced'], equals(0));
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        final original = Branch(
          id: 'test-id',
          name: 'Original Name',
          address: 'Original Address',
        );

        final updated = original.copyWith(
          name: 'Updated Name',
          isSynced: true,
        );

        expect(updated.id, equals('test-id'));
        expect(updated.name, equals('Updated Name'));
        expect(updated.address, equals('Original Address'));
        expect(updated.isSynced, isTrue);
      });

      test('should keep original values when not specified', () {
        final original = Branch(
          id: 'test-id',
          name: 'Original Name',
          address: 'Original Address',
          isSynced: true,
        );

        final updated = original.copyWith(name: 'Updated Name');

        expect(updated.id, equals('test-id'));
        expect(updated.name, equals('Updated Name'));
        expect(updated.address, equals('Original Address'));
        expect(updated.isSynced, isTrue);
      });
    });

    group('Helper methods', () {
      test('isDeleted should work correctly', () {
        final activeBranch = Branch(id: 'test-id', name: 'Test');
        final deletedBranch = Branch(
          id: 'test-id',
          name: 'Test',
          deletedAt: DateTime.now(),
        );

        expect(activeBranch.isDeleted, isFalse);
        expect(deletedBranch.isDeleted, isTrue);
      });

      test('isActive should work correctly', () {
        final activeBranch = Branch(id: 'test-id', name: 'Test');
        final deletedBranch = Branch(
          id: 'test-id',
          name: 'Test',
          deletedAt: DateTime.now(),
        );

        expect(activeBranch.isActive, isTrue);
        expect(deletedBranch.isActive, isFalse);
      });
    });

    group('Factory methods', () {
      test('create should set timestamps', () {
        final branch = Branch.create(
          id: 'test-id',
          name: 'Test Branch',
          address: 'Test Address',
        );

        expect(branch.id, equals('test-id'));
        expect(branch.name, equals('Test Branch'));
        expect(branch.address, equals('Test Address'));
        expect(branch.createdAt, isNotNull);
        expect(branch.updatedAt, isNotNull);
        expect(branch.isSynced, isFalse);
      });

      test('update should update timestamp and sync status', () {
        final original = Branch.create(
          id: 'test-id',
          name: 'Original Name',
        );

        final updated = original.update(
          name: 'Updated Name',
          address: 'New Address',
        );

        expect(updated.name, equals('Updated Name'));
        expect(updated.address, equals('New Address'));
        expect(updated.updatedAt, isNot(equals(original.updatedAt)));
        expect(updated.isSynced, isFalse);
      });

      test('markAsDeleted should set deletedAt', () {
        final branch = Branch.create(id: 'test-id', name: 'Test');
        final deleted = branch.markAsDeleted();

        expect(deleted.deletedAt, isNotNull);
        expect(deleted.isDeleted, isTrue);
        expect(deleted.isSynced, isFalse);
      });

      test('markAsSynced should set synced flag', () {
        final branch = Branch.create(id: 'test-id', name: 'Test');
        final synced = branch.markAsSynced();

        expect(synced.isSynced, isTrue);
      });
    });

    group('Equality', () {
      test('should be equal when IDs match', () {
        final branch1 = Branch(id: 'test-id', name: 'Branch 1');
        final branch2 = Branch(id: 'test-id', name: 'Branch 2');

        expect(branch1, equals(branch2));
        expect(branch1.hashCode, equals(branch2.hashCode));
      });

      test('should not be equal when IDs differ', () {
        final branch1 = Branch(id: 'test-id-1', name: 'Branch');
        final branch2 = Branch(id: 'test-id-2', name: 'Branch');

        expect(branch1, isNot(equals(branch2)));
        expect(branch1.hashCode, isNot(equals(branch2.hashCode)));
      });
    });

    group('toString', () {
      test('should return readable string representation', () {
        final branch = Branch(
          id: 'test-id',
          name: 'Test Branch',
          address: 'Test Address',
          isSynced: true,
        );

        final string = branch.toString();

        expect(string, contains('test-id'));
        expect(string, contains('Test Branch'));
        expect(string, contains('Test Address'));
        expect(string, contains('true'));
      });
    });
  });
}
