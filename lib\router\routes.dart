class AppRoutes {
  // Root routes
  static const String splash = '/';
  static const String dashboard = '/dashboard';
  static const String login = '/login';

  // Inventory routes
  static const String inventory = '/inventory';
  static const String products = '/inventory/products';
  static const String productAdd = '/inventory/products/add';
  static const String productEdit = '/inventory/products/edit';
  static const String productView = '/inventory/products/view';
  static const String categories = '/inventory/categories';
  static const String categoryAdd = '/inventory/categories/add';
  static const String categoryEdit = '/inventory/categories/edit';
  static const String units = '/inventory/units';
  static const String unitAdd = '/inventory/units/add';
  static const String unitEdit = '/inventory/units/edit';
  static const String stockLog = '/inventory/stock-log';
  static const String stockAdjustment = '/inventory/stock-adjustment';

  // Sales routes
  static const String sales = '/sales';
  static const String pos = '/sales/pos';
  static const String saleAdd = '/sales/add';
  static const String saleList = '/sales/list';
  static const String saleView = '/sales/view';
  static const String saleEdit = '/sales/edit';
  static const String saleReturn = '/sales/return';

  // Purchase routes
  static const String purchases = '/purchases';
  static const String purchaseList = '/purchases/list';
  static const String purchaseAdd = '/purchases/add';
  static const String purchaseEdit = '/purchases/edit';
  static const String purchaseView = '/purchases/view';
  static const String purchaseReturn = '/purchases/return';

  // Accounts routes
  static const String accounts = '/accounts';
  static const String customers = '/accounts/customers';
  static const String customerAdd = '/accounts/customers/add';
  static const String customerEdit = '/accounts/customers/edit';
  static const String customerView = '/accounts/customers/view';
  static const String suppliers = '/accounts/suppliers';
  static const String supplierAdd = '/accounts/suppliers/add';
  static const String supplierEdit = '/accounts/suppliers/edit';
  static const String supplierView = '/accounts/suppliers/view';

  // Cash routes
  static const String cash = '/cash';
  static const String cashBoxes = '/cash/boxes';
  static const String cashBoxAdd = '/cash/boxes/add';
  static const String cashBoxEdit = '/cash/boxes/edit';
  static const String transactions = '/cash/transactions';
  static const String transactionAdd = '/cash/transactions/add';
  static const String transactionView = '/cash/transactions/view';
  static const String receipt = '/cash/receipt';
  static const String payment = '/cash/payment';

  // Employee routes
  static const String employees = '/employees';
  static const String employeeList = '/employees/list';
  static const String employeeAdd = '/employees/add';
  static const String employeeEdit = '/employees/edit';
  static const String employeeView = '/employees/view';
  static const String salaries = '/employees/salaries';
  static const String salaryAdd = '/employees/salaries/add';
  static const String salaryView = '/employees/salaries/view';
  static const String salaryList = '/employees/salaries/list';

  // Reports routes
  static const String reports = '/reports';
  static const String salesReport = '/reports/sales';
  static const String purchaseReport = '/reports/purchases';
  static const String inventoryReport = '/reports/inventory';
  static const String customerReport = '/reports/customers';
  static const String supplierReport = '/reports/suppliers';
  static const String cashFlowReport = '/reports/cash-flow';
  static const String profitLossReport = '/reports/profit-loss';
  static const String taxReport = '/reports/tax';

  // Settings routes
  static const String settings = '/settings';
  static const String userManagement = '/settings/users';

  // AI routes
  static const String aiDashboard = '/ai-dashboard';
  static const String userAdd = '/settings/users/add';
  static const String userEdit = '/settings/users/edit';
  static const String companyInfo = '/settings/company';
  static const String printerSettings = '/settings/printer';
  static const String taxSettings = '/settings/tax';
  static const String backupRestore = '/settings/backup';
  static const String branches = '/settings/branches';
  static const String branchAdd = '/settings/branches/add';
  static const String branchEdit = '/settings/branches/edit';

  // Utility routes
  static const String sync = '/sync';
  static const String profile = '/profile';
  static const String about = '/about';
  static const String help = '/help';

  // Route parameters
  static const String idParam = 'id';
  static const String typeParam = 'type';
  static const String modeParam = 'mode';

  // Route parameter patterns
  static String productEditWithId(String id) => '$productEdit/$id';
  static String productViewWithId(String id) => '$productView/$id';
  static String categoryEditWithId(String id) => '$categoryEdit/$id';
  static String unitEditWithId(String id) => '$unitEdit/$id';
  static String saleViewWithId(String id) => '$saleView/$id';
  static String saleEditWithId(String id) => '$saleEdit/$id';
  static String purchaseViewWithId(String id) => '$purchaseView/$id';
  static String purchaseEditWithId(String id) => '$purchaseEdit/$id';
  static String customerViewWithId(String id) => '$customerView/$id';
  static String customerEditWithId(String id) => '$customerEdit/$id';
  static String supplierViewWithId(String id) => '$supplierView/$id';
  static String supplierEditWithId(String id) => '$supplierEdit/$id';
  static String employeeViewWithId(String id) => '$employeeView/$id';
  static String employeeEditWithId(String id) => '$employeeEdit/$id';
  static String cashBoxEditWithId(String id) => '$cashBoxEdit/$id';
  static String transactionViewWithId(String id) => '$transactionView/$id';
  static String salaryViewWithId(String id) => '$salaryView/$id';
  static String userEditWithId(String id) => '$userEdit/$id';
  static String branchEditWithId(String id) => '$branchEdit/$id';

  // Route groups for navigation
  static const List<String> mainRoutes = [
    dashboard,
    inventory,
    sales,
    purchases,
    accounts,
    cash,
    employees,
    reports,
    settings,
  ];

  static const List<String> inventoryRoutes = [
    products,
    categories,
    units,
    stockLog,
    stockAdjustment,
  ];

  static const List<String> salesRoutes = [
    pos,
    saleAdd,
    saleList,
  ];

  static const List<String> purchaseRoutes = [
    purchaseList,
    purchaseAdd,
  ];

  static const List<String> accountRoutes = [
    customers,
    suppliers,
  ];

  static const List<String> cashRoutes = [
    cashBoxes,
    transactions,
  ];

  static const List<String> employeeRoutes = [
    employeeList,
    salaries,
  ];

  static const List<String> reportRoutes = [
    salesReport,
    purchaseReport,
    inventoryReport,
    customerReport,
    supplierReport,
    cashFlowReport,
    profitLossReport,
    taxReport,
  ];

  static const List<String> settingRoutes = [
    userManagement,
    companyInfo,
    printerSettings,
    taxSettings,
    backupRestore,
    branches,
  ];

  // Helper methods
  static bool isMainRoute(String route) => mainRoutes.contains(route);
  static bool isInventoryRoute(String route) => route.startsWith(inventory);
  static bool isSalesRoute(String route) => route.startsWith(sales);
  static bool isPurchaseRoute(String route) => route.startsWith(purchases);
  static bool isAccountRoute(String route) => route.startsWith(accounts);
  static bool isCashRoute(String route) => route.startsWith(cash);
  static bool isEmployeeRoute(String route) => route.startsWith(employees);
  static bool isReportRoute(String route) => route.startsWith(reports);
  static bool isSettingRoute(String route) => route.startsWith(settings);

  // Get route title
  static String getRouteTitle(String route) {
    switch (route) {
      case dashboard:
        return 'لوحة التحكم';
      case inventory:
        return 'المخزون';
      case products:
        return 'المنتجات';
      case categories:
        return 'التصنيفات';
      case units:
        return 'وحدات القياس';
      case sales:
        return 'المبيعات';
      case pos:
        return 'نقطة البيع';
      case saleAdd:
        return 'إضافة فاتورة مبيعات';
      case saleList:
        return 'قائمة المبيعات';
      case purchases:
        return 'المشتريات';
      case purchaseList:
        return 'قائمة المشتريات';
      case accounts:
        return 'الحسابات';
      case customers:
        return 'العملاء';
      case suppliers:
        return 'الموردين';
      case cash:
        return 'الصناديق';
      case cashBoxes:
        return 'الصناديق';
      case transactions:
        return 'المعاملات';
      case employees:
        return 'الموظفين';
      case employeeList:
        return 'قائمة الموظفين';
      case salaries:
        return 'الرواتب';
      case reports:
        return 'التقارير';
      case settings:
        return 'الإعدادات';
      default:
        return 'تجاري تك';
    }
  }
}
