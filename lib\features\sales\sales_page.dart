import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';

class SalesPage extends ConsumerStatefulWidget {
  const SalesPage({super.key});

  @override
  ConsumerState<SalesPage> createState() => _SalesPageState();
}

class _SalesPageState extends ConsumerState<SalesPage> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'طباعة المستند',
      actions: [
        Container(
          margin: EdgeInsets.only(left: 16.w),
          child: TextButton.icon(
            onPressed: () => context.go(AppRoutes.pos),
            icon: const Icon(Icons.add, color: Colors.white),
            label: Text(
              'قيد متعدد',
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            style: TextButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ),
      ],
      child: Column(
        children: [
          // Header section with date and search
          _buildHeaderSection(context),

          // Data table
          Expanded(
            flex: 2,
            child: _buildDataTable(context),
          ),

          // Summary section
          _buildSummarySection(context),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Column(
        children: [
          // Date and invoice number row
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رقم الفاتورة أو الإيصال اليدوي',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '2',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الرقم',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        color: Colors.green[100],
                        border: Border.all(color: Colors.green[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today,
                              size: 16.r, color: Colors.green[700]),
                          SizedBox(width: 8.w),
                          Text(
                            '19/04/2024',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Search row
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث',
                      prefixIcon: const Icon(Icons.search),
                      border: InputBorder.none,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                'عميل عام',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Amount and currency row
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المبلغ',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '597',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'سعر الصرف',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: 'ر.س',
                          isExpanded: true,
                          items: ['ر.س', 'دولار', 'يورو'].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            // Handle currency change
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'العملة',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: 'ر.س',
                          isExpanded: true,
                          items: ['ر.س', 'دولار', 'يورو'].map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            // Handle currency change
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Notes section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الملاحظة',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  'المقابل',
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Handle debit action
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  child: Text('مدين/عليه'),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Handle credit action
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  child: Text('دائن/له'),
                ),
              ),
              SizedBox(width: 12.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(3.r),
                ),
                child: Text(
                  '10',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Table header
          Container(
            color: AppColors.primary,
            child: Row(
              children: [
                _buildTableHeaderCell('م', flex: 1),
                _buildTableHeaderCell('اسم الحساب', flex: 3),
                _buildTableHeaderCell('العملة', flex: 2),
                _buildTableHeaderCell('مد ح', flex: 2),
                _buildTableHeaderCell('مدين/عليه', flex: 2),
              ],
            ),
          ),

          // Table rows
          Expanded(
            child: ListView.builder(
              itemCount: _getSampleData().length,
              itemBuilder: (context, index) {
                final item = _getSampleData()[index];
                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      _buildTableCell(
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            IconButton(
                              icon: Icon(Icons.edit,
                                  color: Colors.green, size: 20.r),
                              onPressed: () {
                                // Handle edit
                              },
                            ),
                            IconButton(
                              icon: Icon(Icons.delete,
                                  color: Colors.red, size: 20.r),
                              onPressed: () {
                                // Handle delete
                              },
                            ),
                          ],
                        ),
                        flex: 1,
                      ),
                      _buildTableCell(Text(item['name']!), flex: 3),
                      _buildTableCell(Text(item['currency']!), flex: 2),
                      _buildTableCell(Text(item['rate']!), flex: 2),
                      _buildTableCell(Text(item['amount']!), flex: 2),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeaderCell(String text, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
        child: Text(
          text,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildTableCell(Widget child, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
        child: child,
      ),
    );
  }

  Widget _buildSummarySection(BuildContext context) {
    return Container(
      color: Colors.grey[100],
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'اسم العملة',
                  'دولار',
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي عليهم',
                  '321',
                  Colors.red,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي لهم',
                  '600',
                  Colors.green,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  'الفارق',
                  '279',
                  Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  '',
                  'ريال',
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '39,600',
                  Colors.red,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '12,123',
                  Colors.green,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '27,477',
                  Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  '',
                  'ريال سعودي',
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '330',
                  Colors.red,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '240',
                  Colors.green,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildSummaryCard(
                  '',
                  '90',
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Column(
        children: [
          if (title.isNotEmpty)
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Map<String, String>> _getSampleData() {
    return [
      {
        'name': 'عميل عام',
        'currency': 'ريال',
        'rate': '1',
        'amount': '0',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال',
        'rate': '1',
        'amount': '3600',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال',
        'rate': '1',
        'amount': '0',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال',
        'rate': '1',
        'amount': '36000',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال سعودي',
        'rate': '157',
        'amount': '0',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال سعودي',
        'rate': '157',
        'amount': '120',
      },
      {
        'name': 'عميل عام',
        'currency': 'ريال سعودي',
        'rate': '157',
        'amount': '210',
      },
    ];
  }
}
