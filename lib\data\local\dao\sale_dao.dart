import 'package:sqflite/sqflite.dart';
import 'package:tijari_tech/data/local/database_constants.dart';
import 'base_dao.dart';
import '../../models/sale.dart';
import '../../models/sale_item.dart';
import '../../../core/utils/app_utils.dart';

class SaleDao extends BaseDao<Sale> {
  @override
  String get tableName => DatabaseConstants.tableSales;

  @override
  Sale fromMap(Map<String, dynamic> map) => Sale.fromMap(map);

  @override
  Map<String, dynamic> toMap(Sale entity) => entity.toMap();

  // Create sale with items
  Future<String> createSaleWithItems(Sale sale, List<SaleItem> items) async {
    try {
      final db = await database;
      String saleId = '';
      
      await db.transaction((txn) async {
        // Insert sale
        final saleMap = toMap(sale);
        saleMap[DatabaseConstants.columnSaleCreatedAt] = DateTime.now().toIso8601String();
        saleMap[DatabaseConstants.columnSaleUpdatedAt] = saleMap[DatabaseConstants.columnSaleCreatedAt];
        saleMap[DatabaseConstants.columnSaleIsSynced] = 0;
        
        if (saleMap[DatabaseConstants.columnSaleId] == null || saleMap[DatabaseConstants.columnSaleId].toString().isEmpty) {
          saleMap[DatabaseConstants.columnSaleId] = AppUtils.generateId();
        }
        saleId = saleMap[DatabaseConstants.columnSaleId];
        
        await txn.insert(DatabaseConstants.tableSales, saleMap);
        
        // Insert sale items
        for (final item in items) {
          final itemMap = item.toMap();
          itemMap[DatabaseConstants.columnSaleItemSaleId] = saleId;
          itemMap[DatabaseConstants.columnSaleItemCreatedAt] = saleMap[DatabaseConstants.columnSaleCreatedAt];
          itemMap[DatabaseConstants.columnSaleItemUpdatedAt] = saleMap[DatabaseConstants.columnSaleCreatedAt];
          itemMap[DatabaseConstants.columnSaleItemIsSynced] = 0;
          
          if (itemMap[DatabaseConstants.columnSaleItemId] == null || itemMap[DatabaseConstants.columnSaleItemId].toString().isEmpty) {
            itemMap[DatabaseConstants.columnSaleItemId] = AppUtils.generateId();
          }
          
          await txn.insert(DatabaseConstants.tableSaleItems, itemMap);
          
          // Update stock
          await _updateStock(txn, item.productId, item.qty, 'out');
        }
        
        // Create stock movements
        await _createStockMovements(txn, saleId, items);
        
        // Update customer balance if credit sale
        if (sale.customerId != null && sale.due > 0) {
          await _updateCustomerBalance(txn, sale.customerId!, sale.due);
        }
        
        // Create transaction record
        await _createTransaction(txn, saleId, sale);
      });
      
      AppUtils.logInfo('Created sale with ${items.length} items, ID: $saleId');
      return saleId;
    } catch (e) {
      AppUtils.logError('Error creating sale with items', e);
      throw Exception('Failed to create sale: ${e.toString()}');
    }
  }

  // Get sale with items
  Future<Map<String, dynamic>?> getSaleWithItems(String saleId) async {
    final sql = '''
      SELECT 
        s.*,
        c.${DatabaseConstants.columnCustomerName} as customer_name,
        b.${DatabaseConstants.columnBranchName} as branch_name
      FROM ${DatabaseConstants.tableSales} s
      LEFT JOIN ${DatabaseConstants.tableCustomers} c ON s.${DatabaseConstants.columnSaleCustomerId} = c.${DatabaseConstants.columnCustomerId}
      LEFT JOIN ${DatabaseConstants.tableBranches} b ON s.${DatabaseConstants.columnSaleBranchId} = b.${DatabaseConstants.columnBranchId}
      WHERE s.${DatabaseConstants.columnSaleId} = ? AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
    ''';
    
    final saleResult = await rawQuery(sql, [saleId]);
    if (saleResult.isEmpty) return null;
    
    final sale = saleResult.first;
    
    // Get sale items
    final itemsSql = '''
      SELECT 
        si.*,
        p.${DatabaseConstants.columnProductNameAr} as product_name,
        p.${DatabaseConstants.columnProductBarcode},
        u.${DatabaseConstants.columnUnitName} as unit_name
      FROM ${DatabaseConstants.tableSaleItems} si
      JOIN ${DatabaseConstants.tableProducts} p ON si.${DatabaseConstants.columnSaleItemProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u ON si.${DatabaseConstants.columnSaleItemUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE si.${DatabaseConstants.columnSaleItemSaleId} = ?
      ORDER BY si.${DatabaseConstants.columnSaleItemCreatedAt}
    ''';
    
    final items = await rawQuery(itemsSql, [saleId]);
    
    return {
      'sale': sale,
      'items': items,
    };
  }

  // Get sales by date range
  Future<List<Map<String, dynamic>>> getSalesByDateRange(DateTime startDate, DateTime endDate) async {
    final sql = '''
      SELECT 
        s.*,
        c.${DatabaseConstants.columnCustomerName} as customer_name,
        COUNT(si.${DatabaseConstants.columnSaleItemId}) as item_count
      FROM ${DatabaseConstants.tableSales} s
      LEFT JOIN ${DatabaseConstants.tableCustomers} c ON s.${DatabaseConstants.columnSaleCustomerId} = c.${DatabaseConstants.columnCustomerId}
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si ON s.${DatabaseConstants.columnSaleId} = si.${DatabaseConstants.columnSaleItemSaleId}
      WHERE s.${DatabaseConstants.columnSaleSaleDate} >= ? AND s.${DatabaseConstants.columnSaleSaleDate} <= ? AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY s.${DatabaseConstants.columnSaleId}
      ORDER BY s.${DatabaseConstants.columnSaleSaleDate} DESC, s.${DatabaseConstants.columnSaleCreatedAt} DESC
    ''';
    
    return await rawQuery(sql, [
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);
  }

  // Get sales by customer
  Future<List<Map<String, dynamic>>> getSalesByCustomer(String customerId) async {
    final sql = '''
      SELECT 
        s.*,
        COUNT(si.${DatabaseConstants.columnSaleItemId}) as item_count
      FROM ${DatabaseConstants.tableSales} s
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si ON s.${DatabaseConstants.columnSaleId} = si.${DatabaseConstants.columnSaleItemSaleId}
      WHERE s.${DatabaseConstants.columnSaleCustomerId} = ? AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY s.${DatabaseConstants.columnSaleId}
      ORDER BY s.${DatabaseConstants.columnSaleSaleDate} DESC
    ''';
    
    return await rawQuery(sql, [customerId]);
  }

  // Get daily sales summary
  Future<List<Map<String, dynamic>>> getDailySalesSummary({int days = 30}) async {
    final startDate = DateTime.now().subtract(Duration(days: days));
    
    final sql = '''
      SELECT 
        DATE(${DatabaseConstants.columnSaleSaleDate}) as date,
        COUNT(*) as total_sales,
        SUM(${DatabaseConstants.columnSaleTotalAmount}) as total_amount,
        SUM(${DatabaseConstants.columnSalePaidAmount}) as total_paid,
        SUM(${DatabaseConstants.columnSaleDueAmount}) as total_due,
        AVG(${DatabaseConstants.columnSaleTotalAmount}) as avg_amount
      FROM ${DatabaseConstants.tableSales}
      WHERE ${DatabaseConstants.columnSaleSaleDate} >= ? AND ${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY DATE(${DatabaseConstants.columnSaleSaleDate})
      ORDER BY date DESC
    ''';
    
    return await rawQuery(sql, [startDate.toIso8601String()]);
  }

  // Get top customers by sales
  Future<List<Map<String, dynamic>>> getTopCustomersBySales({int limit = 10}) async {
    final sql = '''
      SELECT 
        c.${DatabaseConstants.columnCustomerId},
        c.${DatabaseConstants.columnCustomerName},
        COUNT(s.${DatabaseConstants.columnSaleId}) as total_sales,
        SUM(s.${DatabaseConstants.columnSaleTotalAmount}) as total_amount,
        SUM(s.${DatabaseConstants.columnSalePaidAmount}) as total_paid,
        SUM(s.${DatabaseConstants.columnSaleDueAmount}) as total_due
      FROM ${DatabaseConstants.tableCustomers} c
      JOIN ${DatabaseConstants.tableSales} s ON c.${DatabaseConstants.columnCustomerId} = s.${DatabaseConstants.columnSaleCustomerId}
      WHERE s.${DatabaseConstants.columnSaleDeletedAt} IS NULL AND c.${DatabaseConstants.columnCustomerDeletedAt} IS NULL
      GROUP BY c.${DatabaseConstants.columnCustomerId}, c.${DatabaseConstants.columnCustomerName}
      ORDER BY total_amount DESC
      LIMIT ?
    ''';
    
    return await rawQuery(sql, [limit]);
  }

  // Get pending payments
  Future<List<Map<String, dynamic>>> getPendingPayments() async {
    final sql = '''
      SELECT 
        s.*,
        c.${DatabaseConstants.columnCustomerName} as customer_name,
        c.${DatabaseConstants.columnCustomerPhone} as customer_phone
      FROM ${DatabaseConstants.tableSales} s
      JOIN ${DatabaseConstants.tableCustomers} c ON s.${DatabaseConstants.columnSaleCustomerId} = c.${DatabaseConstants.columnCustomerId}
      WHERE s.${DatabaseConstants.columnSaleDueAmount} > 0 AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL AND c.${DatabaseConstants.columnCustomerDeletedAt} IS NULL
      ORDER BY s.${DatabaseConstants.columnSaleSaleDate} ASC
    ''';
    
    return await rawQuery(sql);
  }

  // Update sale payment
  Future<bool> updatePayment(String saleId, double paidAmount) async {
    try {
      final db = await database;
      
      await db.transaction((txn) async {
        // Get current sale
        final saleResult = await txn.query(
          DatabaseConstants.tableSales,
          where: '${DatabaseConstants.columnSaleId} = ? AND ${DatabaseConstants.columnSaleDeletedAt} IS NULL',
          whereArgs: [saleId],
          limit: 1,
        );
        
        if (saleResult.isEmpty) {
          throw Exception('Sale not found');
        }
        
        final sale = saleResult.first;
        final currentPaid = (sale[DatabaseConstants.columnSalePaidAmount] as num).toDouble();
        final totalAmount = (sale[DatabaseConstants.columnSaleTotalAmount] as num).toDouble();
        final newPaidAmount = currentPaid + paidAmount;
        final newDueAmount = totalAmount - newPaidAmount;
        
        if (newPaidAmount > totalAmount) {
          throw Exception('Payment amount exceeds total amount');
        }
        
        // Update sale
        await txn.update(
          DatabaseConstants.tableSales,
          {
            DatabaseConstants.columnSalePaidAmount: newPaidAmount,
            DatabaseConstants.columnSaleDueAmount: newDueAmount,
            DatabaseConstants.columnSaleUpdatedAt: DateTime.now().toIso8601String(),
            DatabaseConstants.columnSaleIsSynced: 0,
          },
          where: '${DatabaseConstants.columnSaleId} = ?',
          whereArgs: [saleId],
        );
        
        // Update customer balance
        final customerId = sale[DatabaseConstants.columnSaleCustomerId] as String?;
        if (customerId != null) {
          await _updateCustomerBalance(txn, customerId, -paidAmount);
        }
        
        // Create payment transaction
        await txn.insert(DatabaseConstants.tableTransactions, {
          DatabaseConstants.columnTransactionId: AppUtils.generateId(),
          DatabaseConstants.columnTransactionType: 'receipt',
          DatabaseConstants.columnTransactionAmount: paidAmount,
          // DatabaseConstants.columnTransactionRelatedTo: 'sale',
          // DatabaseConstants.columnTransactionRelatedId: saleId,
          DatabaseConstants.columnTransactionCustomerId: customerId,
          DatabaseConstants.columnTransactionTransactionDate: DateTime.now().toIso8601String(),
          DatabaseConstants.columnTransactionNotes: 'Payment for sale #${sale[DatabaseConstants.columnSaleInvoiceNo]}',
          DatabaseConstants.columnTransactionCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnTransactionIsSynced: 0,
        });
      });
      
      AppUtils.logInfo('Updated payment for sale: $saleId, amount: $paidAmount');
      return true;
    } catch (e) {
      AppUtils.logError('Error updating sale payment', e);
      return false;
    }
  }

  // Cancel sale
  Future<bool> cancelSale(String saleId, String reason) async {
    try {
      final db = await database;
      
      await db.transaction((txn) async {
        // Get sale with items
        final saleResult = await txn.query(
          DatabaseConstants.tableSales,
          where: '${DatabaseConstants.columnSaleId} = ? AND ${DatabaseConstants.columnSaleDeletedAt} IS NULL',
          whereArgs: [saleId],
          limit: 1,
        );
        
        if (saleResult.isEmpty) {
          throw Exception('Sale not found');
        }
        
        final sale = saleResult.first;
        
        // Get sale items
        final items = await txn.query(
          DatabaseConstants.tableSaleItems,
          where: '${DatabaseConstants.columnSaleItemSaleId} = ?',
          whereArgs: [saleId],
        );
        
        // Restore stock for each item
        for (final item in items) {
          await _updateStock(txn, item[DatabaseConstants.columnSaleItemProductId] as String, 
                           (item[DatabaseConstants.columnSaleItemQty] as num).toDouble(), 'in');
        }
        
        // Update customer balance if there was due amount
        final customerId = sale[DatabaseConstants.columnSaleCustomerId] as String?;
        final dueAmount = (sale[DatabaseConstants.columnSaleDueAmount] as num).toDouble();
        if (customerId != null && dueAmount > 0) {
          await _updateCustomerBalance(txn, customerId, -dueAmount);
        }
        
        // Soft delete sale and items
        final now = DateTime.now().toIso8601String();
        
        await txn.update(
          DatabaseConstants.tableSales,
          {
            DatabaseConstants.columnSaleDeletedAt: now,
            DatabaseConstants.columnSaleUpdatedAt: now,
            DatabaseConstants.columnSaleNotes: '${sale[DatabaseConstants.columnSaleNotes] ?? ''}\nCancelled: $reason',
            DatabaseConstants.columnSaleIsSynced: 0,
          },
          where: '${DatabaseConstants.columnSaleId} = ?',
          whereArgs: [saleId],
        );
        
        await txn.update(
          DatabaseConstants.tableSaleItems,
          {
            DatabaseConstants.columnSaleItemDeletedAt: now,
            DatabaseConstants.columnSaleItemUpdatedAt: now,
            DatabaseConstants.columnSaleItemIsSynced: 0,
          },
          where: '${DatabaseConstants.columnSaleItemSaleId} = ?',
          whereArgs: [saleId],
        );
        
        // Create cancellation transaction
        await txn.insert(DatabaseConstants.tableTransactions, {
          DatabaseConstants.columnTransactionId: AppUtils.generateId(),
          DatabaseConstants.columnTransactionType: 'refund',
          DatabaseConstants.columnTransactionAmount: sale[DatabaseConstants.columnSalePaidAmount],
          // DatabaseConstants.columnTransactionRelatedTo: 'sale',
          // DatabaseConstants.columnTransactionRelatedId: saleId,
          DatabaseConstants.columnTransactionCustomerId: customerId,
          DatabaseConstants.columnTransactionTransactionDate: now,
          DatabaseConstants.columnTransactionNotes: 'Sale cancellation: $reason',
          DatabaseConstants.columnTransactionCreatedAt: now,
          DatabaseConstants.columnTransactionIsSynced: 0,
        });
      });
      
      AppUtils.logInfo('Cancelled sale: $saleId, reason: $reason');
      return true;
    } catch (e) {
      AppUtils.logError('Error cancelling sale', e);
      return false;
    }
  }

  // Helper methods
  Future<void> _updateStock(Transaction txn, String productId, double qty, String type) async {
    final stockResult = await txn.query(
      DatabaseConstants.tableStocks,
      where: '${DatabaseConstants.columnStockProductId} = ?',
      whereArgs: [productId],
      limit: 1,
    );
    
    if (stockResult.isNotEmpty) {
      final currentQty = (stockResult.first[DatabaseConstants.columnStockQuantity] as num).toDouble();
      final newQty = type == 'out' ? currentQty - qty : currentQty + qty;
      
      await txn.update(
        DatabaseConstants.tableStocks,
        {
          DatabaseConstants.columnStockQuantity: newQty,
          DatabaseConstants.columnStockUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnStockIsSynced: 0,
        },
        where: '${DatabaseConstants.columnStockProductId} = ?',
        whereArgs: [productId],
      );
    }
  }

  Future<void> _createStockMovements(Transaction txn, String saleId, List<SaleItem> items) async {
    for (final item in items) {
      await txn.insert(DatabaseConstants.tableStockMovements, {
        DatabaseConstants.columnStockMovementId: AppUtils.generateId(),
        DatabaseConstants.columnStockMovementProductId: item.productId,
        DatabaseConstants.columnStockMovementType: 'out',
        DatabaseConstants.columnStockMovementQty: item.qty,
        DatabaseConstants.columnStockMovementUnitId: item.unitId,
        DatabaseConstants.columnStockMovementReferenceType: 'sale',
        DatabaseConstants.columnStockMovementReferenceId: saleId,
        DatabaseConstants.columnStockMovementNotes: 'Sale transaction',
        DatabaseConstants.columnStockMovementCreatedAt: DateTime.now().toIso8601String(),
        DatabaseConstants.columnStockMovementIsSynced: 0,
      });
    }
  }

  Future<void> _updateCustomerBalance(Transaction txn, String customerId, double amount) async {
    await txn.rawUpdate('''
      UPDATE ${DatabaseConstants.tableCustomers} 
      SET ${DatabaseConstants.columnCustomerBalance} = ${DatabaseConstants.columnCustomerBalance} + ?, 
          ${DatabaseConstants.columnCustomerUpdatedAt} = ?, 
          ${DatabaseConstants.columnCustomerIsSynced} = 0 
      WHERE ${DatabaseConstants.columnCustomerId} = ?
    ''', [amount, DateTime.now().toIso8601String(), customerId]);
  }

  Future<void> _createTransaction(Transaction txn, String saleId, Sale sale) async {
    if (sale.paid > 0) {
      await txn.insert(DatabaseConstants.tableTransactions, {
        DatabaseConstants.columnTransactionId: AppUtils.generateId(),
        DatabaseConstants.columnTransactionType: 'receipt',
        DatabaseConstants.columnTransactionAmount: sale.paid,
        // DatabaseConstants.columnTransactionRelatedTo: 'sale',
        // DatabaseConstants.columnTransactionRelatedId: saleId,
        DatabaseConstants.columnTransactionCustomerId: sale.customerId,
        DatabaseConstants.columnTransactionTransactionDate: sale.date.toIso8601String(),
        DatabaseConstants.columnTransactionNotes: 'Sale payment #${sale.invoiceNo}',
        DatabaseConstants.columnTransactionCreatedAt: DateTime.now().toIso8601String(),
        DatabaseConstants.columnTransactionIsSynced: 0,
      });
    }
  }
}