import '../services/business_service.dart';
import '../data/repositories/product_repository.dart';
import '../data/repositories/sale_repository.dart';
import '../data/repositories/purchase_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/exceptions/app_exceptions.dart';

/// مثال شامل لاستخدام النظام التجاري
/// يوضح كيفية إضافة المنتجات وعمل عمليات الشراء والبيع
class BusinessOperationsExample {
  final BusinessService _businessService = BusinessService();
  final ProductRepository _productRepository = ProductRepository();
  final SaleRepository _saleRepository = SaleRepository();
  final PurchaseRepository _purchaseRepository = PurchaseRepository();

  /// تشغيل مثال شامل للعمليات التجارية
  Future<void> runCompleteExample() async {
    try {
      print('🚀 بدء تشغيل المثال الشامل للعمليات التجارية');
      print('=' * 50);

      // 1. إضافة منتجات جديدة
      await _addSampleProducts();

      // 2. عمل عملية شراء
      await _createSamplePurchase();

      // 3. عمل عملية بيع
      await _createSampleSale();

      // 4. عرض التقارير
      await _showReports();

      print('✅ تم تشغيل المثال بنجاح!');
      print('=' * 50);
    } catch (e) {
      print('❌ خطأ في تشغيل المثال: $e');
      rethrow;
    }
  }

  /// إضافة منتجات تجريبية
  Future<void> _addSampleProducts() async {
    print('\n📦 إضافة منتجات تجريبية...');

    try {
      // إضافة منتجات فردية
      final product1Id = await _businessService.addNewProduct(
        nameAr: 'لابتوب ديل XPS 13',
        nameEn: 'Dell XPS 13 Laptop',
        barcode: '1234567890123',
        description: 'لابتوب عالي الأداء للأعمال والدراسة',
        costPrice: 3500.0,
        sellingPrice: 4200.0,
        minStock: 5,
        maxStock: 50,
      );
      print('✅ تم إضافة المنتج: لابتوب ديل - ID: $product1Id');

      final product2Id = await _businessService.addNewProduct(
        nameAr: 'ماوس لاسلكي لوجيتك',
        nameEn: 'Logitech Wireless Mouse',
        barcode: '1234567890124',
        description: 'ماوس لاسلكي عالي الدقة',
        costPrice: 45.0,
        sellingPrice: 65.0,
        minStock: 20,
        maxStock: 100,
      );
      print('✅ تم إضافة المنتج: ماوس لاسلكي - ID: $product2Id');

      // إضافة دفعة من المنتجات
      final batchProducts = [
        {
          'nameAr': 'كيبورد ميكانيكي',
          'nameEn': 'Mechanical Keyboard',
          'barcode': '1234567890125',
          'description': 'كيبورد ميكانيكي للألعاب',
          'costPrice': 120.0,
          'sellingPrice': 180.0,
          'minStock': 10.0,
          'maxStock': 50.0,
        },
        {
          'nameAr': 'سماعات بلوتوث',
          'nameEn': 'Bluetooth Headphones',
          'barcode': '1234567890126',
          'description': 'سماعات بلوتوث عالية الجودة',
          'costPrice': 80.0,
          'sellingPrice': 120.0,
          'minStock': 15.0,
          'maxStock': 60.0,
        },
      ];

      final batchIds = await _businessService.addProductsBatch(batchProducts);
      print('✅ تم إضافة ${batchIds.length} منتج في دفعة واحدة');

      // عرض إجمالي المنتجات
      final totalProducts = await _productRepository.countProducts();
      print('📊 إجمالي المنتجات في النظام: $totalProducts');

    } catch (e) {
      print('❌ خطأ في إضافة المنتجات: $e');
      rethrow;
    }
  }

  /// إنشاء عملية شراء تجريبية
  Future<void> _createSamplePurchase() async {
    print('\n🛒 إنشاء عملية شراء تجريبية...');

    try {
      // الحصول على المنتجات المتاحة
      final products = await _productRepository.getActiveProducts();
      if (products.isEmpty) {
        throw BusinessLogicException('لا توجد منتجات متاحة للشراء');
      }

      // إنشاء عناصر الشراء
      final purchaseItems = <Map<String, dynamic>>[];
      
      // شراء أول منتجين
      for (int i = 0; i < 2 && i < products.length; i++) {
        final product = products[i];
        purchaseItems.add({
          'productId': product.id,
          'qty': 10.0,
          'unitPrice': product.costPrice,
          'unitId': product.baseUnitId,
        });
      }

      // إنشاء عملية الشراء
      final purchaseId = await _businessService.createPurchaseTransaction(
        supplierId: 'supplier-1', // يجب أن يكون موجود في قاعدة البيانات
        items: purchaseItems,
        paidAmount: 1000.0, // دفع جزئي
        notes: 'عملية شراء تجريبية من المثال',
      );

      print('✅ تم إنشاء عملية الشراء بنجاح - ID: $purchaseId');

      // عرض تفاصيل الشراء
      final purchaseDetails = await _purchaseRepository.getPurchaseById(purchaseId);
      if (purchaseDetails != null) {
        final purchase = purchaseDetails['purchase'];
        final items = purchaseDetails['items'] as List;
        
        print('📋 تفاصيل الشراء:');
        print('   - رقم الفاتورة: ${purchase['invoice_no']}');
        print('   - الإجمالي: ${purchase['total_amount']} ر.س');
        print('   - المدفوع: ${purchase['paid_amount']} ر.س');
        print('   - المستحق: ${purchase['due_amount']} ر.س');
        print('   - عدد العناصر: ${items.length}');
      }

    } catch (e) {
      print('❌ خطأ في إنشاء عملية الشراء: $e');
      rethrow;
    }
  }

  /// إنشاء عملية بيع تجريبية
  Future<void> _createSampleSale() async {
    print('\n💰 إنشاء عملية بيع تجريبية...');

    try {
      // الحصول على المنتجات المتاحة
      final products = await _productRepository.getActiveProducts();
      if (products.isEmpty) {
        throw BusinessLogicException('لا توجد منتجات متاحة للبيع');
      }

      // إنشاء عناصر البيع
      final saleItems = <Map<String, dynamic>>[];
      
      // بيع أول منتج
      final product = products.first;
      saleItems.add({
        'productId': product.id,
        'qty': 2.0,
        'unitPrice': product.sellingPrice,
        'unitId': product.baseUnitId,
      });

      // إنشاء عملية البيع
      final saleId = await _businessService.createSaleTransaction(
        customerId: 'customer-1', // يجب أن يكون موجود في قاعدة البيانات
        items: saleItems,
        paidAmount: product.sellingPrice * 2, // دفع كامل
        notes: 'عملية بيع تجريبية من المثال',
      );

      print('✅ تم إنشاء عملية البيع بنجاح - ID: $saleId');

      // عرض تفاصيل البيع
      final saleDetails = await _saleRepository.getSaleById(saleId);
      if (saleDetails != null) {
        final sale = saleDetails['sale'];
        final items = saleDetails['items'] as List;
        
        print('📋 تفاصيل البيع:');
        print('   - رقم الفاتورة: ${sale['invoice_no']}');
        print('   - الإجمالي: ${sale['total_amount']} ر.س');
        print('   - المدفوع: ${sale['paid_amount']} ر.س');
        print('   - المستحق: ${sale['due_amount']} ر.س');
        print('   - عدد العناصر: ${items.length}');
      }

    } catch (e) {
      print('❌ خطأ في إنشاء عملية البيع: $e');
      rethrow;
    }
  }

  /// عرض التقارير
  Future<void> _showReports() async {
    print('\n📊 عرض التقارير...');

    try {
      // تقرير المبيعات اليومية
      final salesReport = await _businessService.getDailySalesReport();
      print('📈 تقرير المبيعات اليومية:');
      print('   - عدد المعاملات: ${salesReport['total_transactions']}');
      print('   - إجمالي المبيعات: ${salesReport['total_sales']} ر.س');
      print('   - إجمالي المدفوع: ${salesReport['total_paid']} ر.س');
      print('   - معدل الدفع: ${salesReport['payment_rate'].toStringAsFixed(1)}%');

      // تقرير المشتريات اليومية
      final purchasesReport = await _businessService.getDailyPurchasesReport();
      print('📉 تقرير المشتريات اليومية:');
      print('   - عدد المعاملات: ${purchasesReport['total_transactions']}');
      print('   - إجمالي المشتريات: ${purchasesReport['total_purchases']} ر.س');
      print('   - إجمالي المدفوع: ${purchasesReport['total_paid']} ر.س');
      print('   - معدل الدفع: ${purchasesReport['payment_rate'].toStringAsFixed(1)}%');

      // إحصائيات المنتجات
      final productAnalytics = await _productRepository.getProductAnalytics();
      print('📦 إحصائيات المنتجات:');
      print('   - إجمالي المنتجات: ${productAnalytics['total_products']}');
      print('   - المنتجات النشطة: ${productAnalytics['active_products']}');
      print('   - المنتجات منخفضة المخزون: ${productAnalytics['low_stock_count']}');

    } catch (e) {
      print('❌ خطأ في عرض التقارير: $e');
      rethrow;
    }
  }

  /// مثال لتحديث المخزون
  Future<void> updateStockExample() async {
    print('\n📦 مثال تحديث المخزون...');

    try {
      final products = await _productRepository.getActiveProducts();
      if (products.isNotEmpty) {
        final product = products.first;
        
        // تحديث المخزون
        final success = await _productRepository.updateStock(
          product.id, 
          'main-warehouse', 
          50.0
        );
        
        if (success) {
          print('✅ تم تحديث مخزون المنتج: ${product.nameAr}');
          
          // عرض المخزون الحالي
          final currentStock = await _productRepository.getCurrentStock(
            product.id, 
            'main-warehouse'
          );
          print('📊 المخزون الحالي: $currentStock');
        }
      }
    } catch (e) {
      print('❌ خطأ في تحديث المخزون: $e');
    }
  }

  /// مثال للبحث في المنتجات
  Future<void> searchProductsExample() async {
    print('\n🔍 مثال البحث في المنتجات...');

    try {
      // البحث بالاسم
      final searchResults = await _productRepository.searchProducts('لابتوب');
      print('🔍 نتائج البحث عن "لابتوب": ${searchResults.length} منتج');
      
      for (final product in searchResults) {
        print('   - ${product.nameAr} (${product.barcode})');
      }

      // البحث بالباركود
      final productByBarcode = await _productRepository.getProductByBarcode('1234567890123');
      if (productByBarcode != null) {
        print('📱 المنتج بالباركود: ${productByBarcode.nameAr}');
      }

    } catch (e) {
      print('❌ خطأ في البحث: $e');
    }
  }
}
