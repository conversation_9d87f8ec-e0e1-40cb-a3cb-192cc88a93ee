import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'sale_item.g.dart';

@JsonSerializable()
class SaleItem {
  final String id;
  final String saleId;
  final String productId;
  final String unitId;
  final double qty;
  final double unitPrice;
  final double total;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const SaleItem({
    required this.id,
    required this.saleId,
    required this.productId,
    required this.unitId,
    required this.qty,
    required this.unitPrice,
    required this.total,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new SaleItem instance from a map
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map[DatabaseConstants.columnSaleItemId] as String,
      saleId: map[DatabaseConstants.columnSaleItemSaleId] as String,
      productId: map[DatabaseConstants.columnSaleItemProductId] as String,
      unitId: map[DatabaseConstants.columnSaleItemUnitId] as String,
      qty: (map[DatabaseConstants.columnSaleItemQty] as num).toDouble(),
      unitPrice:
          (map[DatabaseConstants.columnSaleItemUnitPrice] as num).toDouble(),
      total:
          (map[DatabaseConstants.columnSaleItemTotalPrice] as num).toDouble(),
      createdAt: map[DatabaseConstants.columnSaleItemCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnSaleItemCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnSaleItemUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnSaleItemUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnSaleItemDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnSaleItemDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnSaleItemIsSynced] as int? ?? 0) == 1,
    );
  }

  // Convert SaleItem instance to a map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnSaleItemId: id,
      DatabaseConstants.columnSaleItemSaleId: saleId,
      DatabaseConstants.columnSaleItemProductId: productId,
      DatabaseConstants.columnSaleItemUnitId: unitId,
      DatabaseConstants.columnSaleItemQty: qty,
      DatabaseConstants.columnSaleItemUnitPrice: unitPrice,
      DatabaseConstants.columnSaleItemTotalPrice: total,
      DatabaseConstants.columnSaleItemCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnSaleItemUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnSaleItemDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnSaleItemIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory SaleItem.fromJson(Map<String, dynamic> json) =>
      _$SaleItemFromJson(json);
  Map<String, dynamic> toJson() => _$SaleItemToJson(this);

  // Copy with method for creating modified copies
  SaleItem copyWith({
    String? id,
    String? saleId,
    String? productId,
    String? unitId,
    double? qty,
    double? unitPrice,
    double? total,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      unitId: unitId ?? this.unitId,
      qty: qty ?? this.qty,
      unitPrice: unitPrice ?? this.unitPrice,
      total: total ?? this.total,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SaleItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'SaleItem(id: $id, saleId: $saleId, productId: $productId, qty: $qty, unitPrice: $unitPrice, total: $total, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;

  // Calculate total from quantity and unit price
  double get calculatedTotal => qty * unitPrice;

  // Check if calculated total matches stored total
  bool get isTotalCorrect => (calculatedTotal - total).abs() < 0.01;

  // Create a new sale item with current timestamp
  static SaleItem create({
    required String id,
    required String saleId,
    required String productId,
    required String unitId,
    required double qty,
    required double unitPrice,
  }) {
    final now = DateTime.now();
    final total = qty * unitPrice;

    return SaleItem(
      id: id,
      saleId: saleId,
      productId: productId,
      unitId: unitId,
      qty: qty,
      unitPrice: unitPrice,
      total: total,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update sale item with new timestamp and recalculate total
  SaleItem update({
    String? saleId,
    String? productId,
    String? unitId,
    double? qty,
    double? unitPrice,
  }) {
    final newQty = qty ?? this.qty;
    final newUnitPrice = unitPrice ?? this.unitPrice;
    final newTotal = newQty * newUnitPrice;

    return copyWith(
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      unitId: unitId ?? this.unitId,
      qty: newQty,
      unitPrice: newUnitPrice,
      total: newTotal,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Update quantity and recalculate total
  SaleItem updateQuantity(double newQty) {
    final newTotal = newQty * unitPrice;

    return copyWith(
      qty: newQty,
      total: newTotal,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Update unit price and recalculate total
  SaleItem updateUnitPrice(double newUnitPrice) {
    final newTotal = qty * newUnitPrice;

    return copyWith(
      unitPrice: newUnitPrice,
      total: newTotal,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Apply discount percentage and recalculate total
  SaleItem applyDiscountPercentage(double discountPercentage) {
    final discountAmount = unitPrice * (discountPercentage / 100);
    final newUnitPrice = unitPrice - discountAmount;
    final newTotal = qty * newUnitPrice;

    return copyWith(
      unitPrice: newUnitPrice,
      total: newTotal,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Apply discount amount and recalculate total
  SaleItem applyDiscountAmount(double discountAmount) {
    final newUnitPrice =
        (unitPrice - discountAmount).clamp(0.0, double.infinity);
    final newTotal = qty * newUnitPrice;

    return copyWith(
      unitPrice: newUnitPrice,
      total: newTotal,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  SaleItem markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  SaleItem markAsSynced() {
    return copyWith(isSynced: true);
  }

  // Fix total if calculation is incorrect
  SaleItem fixTotal() {
    if (!isTotalCorrect) {
      return copyWith(
        total: calculatedTotal,
        updatedAt: DateTime.now(),
        isSynced: false,
      );
    }
    return this;
  }

  // Get discount amount (if any)
  double getDiscountAmount(double originalUnitPrice) {
    return originalUnitPrice - unitPrice;
  }

  // Get discount percentage (if any)
  double getDiscountPercentage(double originalUnitPrice) {
    if (originalUnitPrice == 0) return 0.0;
    return ((originalUnitPrice - unitPrice) / originalUnitPrice) * 100;
  }
}
