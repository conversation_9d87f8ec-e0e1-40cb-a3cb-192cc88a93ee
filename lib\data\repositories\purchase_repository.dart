import 'package:tijari_tech/data/local/database_constants.dart';

import '../local/dao/purchase_dao.dart';
import '../models/purchase.dart';
import '../models/purchase_item.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class PurchaseRepository {
  final PurchaseDao _purchaseDao = PurchaseDao();

  // Create purchase with items
  Future<String> createPurchase(Purchase purchase, List<PurchaseItem> items) async {
    try {
      // Validate purchase data
      await _validatePurchase(purchase, items);
      
      return await _purchaseDao.createPurchaseWithItems(purchase, items);
    } catch (e) {
      AppUtils.logError('Error creating purchase', e);
      rethrow;
    }
  }

  // Get all purchases
  Future<List<Map<String, dynamic>>> getAllPurchases({
    int? limit,
    int? offset,
    String? orderBy,
    bool ascending = false,
  }) async {
    try {
      // Get purchases with basic info
      final purchases = await _purchaseDao.findAll(
        orderBy: orderBy ?? DatabaseConstants.columnPurchasePurchaseDate,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
      
      // Convert to map with additional info
      final result = <Map<String, dynamic>>[];
      for (final purchase in purchases) {
        final purchaseMap = purchase.toMap();
        
        // Add supplier name if exists
        if (purchase.supplierId != null) {
          final supplierInfo = await _getSupplierInfo(purchase.supplierId!);
          purchaseMap['supplier_name'] = supplierInfo?[DatabaseConstants.columnSupplierName];
        }
        
        // Add item count
        final itemCount = await _getPurchaseItemCount(purchase.id);
        purchaseMap['item_count'] = itemCount;
        
        result.add(purchaseMap);
      }
      
      return result;
    } catch (e) {
      AppUtils.logError('Error getting all purchases', e);
      return [];
    }
  }

  // Get purchase by ID with items
  Future<Map<String, dynamic>?> getPurchaseById(String id) async {
    try {
      return await _purchaseDao.getPurchaseWithItems(id);
    } catch (e) {
      AppUtils.logError('Error getting purchase by ID', e);
      return null;
    }
  }

  // Get purchases by date range
  Future<List<Map<String, dynamic>>> getPurchasesByDateRange(
    DateTime startDate, 
    DateTime endDate
  ) async {
    try {
      return await _purchaseDao.getPurchasesByDateRange(startDate, endDate);
    } catch (e) {
      AppUtils.logError('Error getting purchases by date range', e);
      return [];
    }
  }

  // Get purchases by supplier
  Future<List<Map<String, dynamic>>> getPurchasesBySupplier(String supplierId) async {
    try {
      return await _purchaseDao.getPurchasesBySupplier(supplierId);
    } catch (e) {
      AppUtils.logError('Error getting purchases by supplier', e);
      return [];
    }
  }

  // Get today's purchases
  Future<List<Map<String, dynamic>>> getTodaysPurchases() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      
      return await getPurchasesByDateRange(startOfDay, endOfDay);
    } catch (e) {
      AppUtils.logError('Error getting today\'s purchases', e);
      return [];
    }
  }

  // Get daily purchases summary
  Future<List<Map<String, dynamic>>> getDailyPurchasesSummary({int days = 30}) async {
    try {
      return await _purchaseDao.getDailyPurchasesSummary(days: days);
    } catch (e) {
      AppUtils.logError('Error getting daily purchases summary', e);
      return [];
    }
  }

  // Get top suppliers by purchases
  Future<List<Map<String, dynamic>>> getTopSuppliers({int limit = 10}) async {
    try {
      return await _purchaseDao.getTopSuppliersByPurchases(limit: limit);
    } catch (e) {
      AppUtils.logError('Error getting top suppliers', e);
      return [];
    }
  }

  // Get pending payments
  Future<List<Map<String, dynamic>>> getPendingPayments() async {
    try {
      return await _purchaseDao.getPendingPayments();
    } catch (e) {
      AppUtils.logError('Error getting pending payments', e);
      return [];
    }
  }

  // Update purchase payment
  Future<bool> updatePayment(String purchaseId, double paidAmount) async {
    try {
      if (paidAmount <= 0) {
        throw ValidationException('Payment amount must be greater than zero');
      }
      
      return await _purchaseDao.updatePayment(purchaseId, paidAmount);
    } catch (e) {
      AppUtils.logError('Error updating purchase payment', e);
      rethrow;
    }
  }

  // Cancel purchase
  Future<bool> cancelPurchase(String purchaseId, String reason) async {
    try {
      if (reason.trim().isEmpty) {
        throw ValidationException('Cancellation reason is required');
      }
      
      return await _purchaseDao.cancelPurchase(purchaseId, reason);
    } catch (e) {
      AppUtils.logError('Error cancelling purchase', e);
      rethrow;
    }
  }

  // Get purchases statistics
  Future<Map<String, dynamic>> getPurchasesStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();
      
      final purchases = await getPurchasesByDateRange(start, end);
      
      double totalAmount = 0;
      double totalPaid = 0;
      double totalDue = 0;
      int totalPurchases = purchases.length;
      
      for (final purchase in purchases) {
        totalAmount += (purchase[DatabaseConstants.columnPurchaseTotalAmount] as num).toDouble();
        totalPaid += (purchase[DatabaseConstants.columnPurchasePaidAmount] as num).toDouble();
        totalDue += (purchase[DatabaseConstants.columnPurchaseDueAmount] as num).toDouble();
      }
      
      return {
        'total_purchases': totalPurchases,
        'total_amount': totalAmount,
        'total_paid': totalPaid,
        'total_due': totalDue,
        'average_purchase': totalPurchases > 0 ? totalAmount / totalPurchases : 0,
        'payment_rate': totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0,
      };
    } catch (e) {
      AppUtils.logError('Error getting purchases statistics', e);
      return {};
    }
  }

  // Generate invoice number
  Future<String> generateInvoiceNumber() async {
    try {
      final today = DateTime.now();
      final datePrefix = '${today.year}${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';
      
      // Get count of purchases today
      final todaysPurchases = await getTodaysPurchases();
      final sequence = (todaysPurchases.length + 1).toString().padLeft(3, '0');
      
      return 'PUR-$datePrefix-$sequence';
    } catch (e) {
      AppUtils.logError('Error generating invoice number', e);
      return 'PUR-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // Search purchases
  Future<List<Map<String, dynamic>>> searchPurchases(String searchTerm) async {
    try {
      if (searchTerm.trim().isEmpty) {
        return await getAllPurchases(limit: 50);
      }
      
      // Search by invoice number or supplier name
      final sql = '''
        SELECT 
          p.*,
          s.${DatabaseConstants.columnSupplierName} as supplier_name,
          COUNT(pi.${DatabaseConstants.columnPurchaseItemId}) as item_count
        FROM ${DatabaseConstants.tablePurchases} p
        LEFT JOIN ${DatabaseConstants.tableSuppliers} s ON p.${DatabaseConstants.columnPurchaseSupplierId} = s.${DatabaseConstants.columnSupplierId}
        LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi ON p.${DatabaseConstants.columnPurchaseId} = pi.${DatabaseConstants.columnPurchaseItemPurchaseId}
        WHERE p.${DatabaseConstants.columnPurchaseDeletedAt} IS NULL 
          AND (p.${DatabaseConstants.columnPurchaseInvoiceNo} LIKE ? OR s.${DatabaseConstants.columnSupplierName} LIKE ?)
        GROUP BY p.${DatabaseConstants.columnPurchaseId}
        ORDER BY p.${DatabaseConstants.columnPurchasePurchaseDate} DESC
        LIMIT 50
      ''';
      
      return await _purchaseDao.rawQuery(sql, ['%$searchTerm%', '%$searchTerm%']);
    } catch (e) {
      AppUtils.logError('Error searching purchases', e);
      return [];
    }
  }

  // Get purchases analytics
  Future<Map<String, dynamic>> getPurchasesAnalytics() async {
    try {
      final today = DateTime.now();
      final startOfMonth = DateTime(today.year, today.month, 1);
      
      // Today's purchases
      final todaysPurchases = await getTodaysPurchases();
      final todaysTotal = todaysPurchases.fold<double>(0, 
          (sum, purchase) => sum + (purchase[DatabaseConstants.columnPurchaseTotalAmount] as num).toDouble());
      
      // This month's purchases
      final monthPurchases = await getPurchasesByDateRange(startOfMonth, today);
      final monthTotal = monthPurchases.fold<double>(0, 
          (sum, purchase) => sum + (purchase[DatabaseConstants.columnPurchaseTotalAmount] as num).toDouble());
      
      // Pending payments
      final pendingPayments = await getPendingPayments();
      final totalDue = pendingPayments.fold<double>(0, 
          (sum, purchase) => sum + (purchase[DatabaseConstants.columnPurchaseDueAmount] as num).toDouble());
      
      return {
        'todays_purchases_count': todaysPurchases.length,
        'todays_purchases_amount': todaysTotal,
        'month_purchases_count': monthPurchases.length,
        'month_purchases_amount': monthTotal,
        'pending_payments_count': pendingPayments.length,
        'total_due_amount': totalDue,
      };
    } catch (e) {
      AppUtils.logError('Error getting purchases analytics', e);
      return {};
    }
  }

  // Validate purchase data
  Future<void> _validatePurchase(Purchase purchase, List<PurchaseItem> items) async {
    if (items.isEmpty) {
      throw ValidationException('Purchase must have at least one item');
    }
    
    if (purchase.totalAmount <= 0) {
      throw ValidationException('Purchase total amount must be greater than zero');
    }
    
    if (purchase.paidAmount < 0) {
      throw ValidationException('Paid amount cannot be negative');
    }
    
    if (purchase.paidAmount > purchase.totalAmount) {
      throw ValidationException('Paid amount cannot exceed total amount');
    }
    
    // Validate items
    for (final item in items) {
      if (item.qty <= 0) {
        throw ValidationException('Item quantity must be greater than zero');
      }
      
      if (item.unitPrice <= 0) {
        throw ValidationException('Item unit price must be greater than zero');
      }
    }
    
    // Validate total calculation
    final calculatedTotal = items.fold<double>(0, (sum, item) => sum + item.totalPrice);
    if ((purchase.totalAmount - calculatedTotal).abs() > 0.01) {
      throw ValidationException('Purchase total does not match sum of items');
    }
  }

  // Helper methods
  Future<Map<String, dynamic>?> _getSupplierInfo(String supplierId) async {
    try {
      final result = await _purchaseDao.rawQuery('''
        SELECT ${DatabaseConstants.columnSupplierName} 
        FROM ${DatabaseConstants.tableSuppliers} 
        WHERE ${DatabaseConstants.columnSupplierId} = ? 
        AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL
      ''', [supplierId]);
      
      return result.isNotEmpty ? result.first : null;
    } catch (e) {
      return null;
    }
  }

  Future<int> _getPurchaseItemCount(String purchaseId) async {
    try {
      final result = await _purchaseDao.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tablePurchaseItems} 
        WHERE ${DatabaseConstants.columnPurchaseItemPurchaseId} = ?
      ''', [purchaseId]);
      
      return result.isNotEmpty ? (result.first['count'] as int) : 0;
    } catch (e) {
      return 0;
    }
  }
}