import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/product.dart';
import '../data/models/category.dart';
import '../data/models/unit.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Product management provider
final productManagementProvider = StateNotifierProvider<ProductManagementNotifier, ProductManagementState>((ref) {
  return ProductManagementNotifier(ref);
});

// Product management state
class ProductManagementState {
  final bool isLoading;
  final String? error;
  final List<Product> products;
  final List<Product> filteredProducts;
  final String searchQuery;
  final String? selectedCategoryId;
  final Product? selectedProduct;

  const ProductManagementState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.filteredProducts = const [],
    this.searchQuery = '',
    this.selectedCategoryId,
    this.selectedProduct,
  });

  ProductManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Product>? products,
    List<Product>? filteredProducts,
    String? searchQuery,
    String? selectedCategoryId,
    Product? selectedProduct,
  }) {
    return ProductManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
      selectedProduct: selectedProduct ?? this.selectedProduct,
    );
  }
}

// Product management notifier
class ProductManagementNotifier extends StateNotifier<ProductManagementState> {
  final Ref _ref;

  ProductManagementNotifier(this._ref) : super(const ProductManagementState()) {
    _loadProducts();
  }

  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all products
  Future<void> _loadProducts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // For now, we'll create a simple product DAO
      // In a real implementation, you would have ProductDao
      final products = <Product>[]; // await productDao.getActiveProducts();
      
      state = state.copyWith(
        isLoading: false,
        products: products,
        filteredProducts: _filterProducts(products),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading products', e);
    }
  }

  // Filter products based on search query and category
  List<Product> _filterProducts(List<Product> products) {
    var filtered = products;

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      filtered = filtered.where((product) {
        return product.nameAr.toLowerCase().contains(state.searchQuery.toLowerCase()) ||
               (product.nameEn?.toLowerCase().contains(state.searchQuery.toLowerCase()) ?? false) ||
               (product.barcode?.contains(state.searchQuery) ?? false);
      }).toList();
    }

    // Filter by category
    if (state.selectedCategoryId != null) {
      filtered = filtered.where((product) {
        return product.categoryId == state.selectedCategoryId;
      }).toList();
    }

    return filtered;
  }

  // Search products
  void searchProducts(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredProducts: _filterProducts(state.products),
    );
  }

  // Filter by category
  void filterByCategory(String? categoryId) {
    state = state.copyWith(
      selectedCategoryId: categoryId,
      filteredProducts: _filterProducts(state.products),
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedCategoryId: null,
      filteredProducts: state.products,
    );
  }

  // Select product
  void selectProduct(Product? product) {
    state = state.copyWith(selectedProduct: product);
  }

  // Refresh products
  Future<void> refresh() async {
    await _loadProducts();
  }

  // Create new product
  Future<bool> createProduct({
    required String nameAr,
    String? nameEn,
    String? barcode,
    String? categoryId,
    String? baseUnitId,
    double reorderPoint = 0.0,
    double sellingPrice=0.0,
    double costPrice=0.0,
    String? imageUrl,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Validate barcode uniqueness if provided
      if (barcode != null && barcode.isNotEmpty) {
        final barcodeExists = await _checkBarcodeExists(barcode);
        if (barcodeExists) {
          throw Exception('الباركود موجود مسبقاً');
        }
      }
      
      final product = Product.create(
        id: AppUtils.generateId(),
        nameAr: nameAr,
        nameEn: nameEn,
        barcode: barcode,
        categoryId: categoryId,
        baseUnitId: baseUnitId,
        reorderPoint: reorderPoint,
        imageUrl: imageUrl, sellingPrice: sellingPrice, costPrice: costPrice,
      );
      
      // In a real implementation, you would save to database
      // final productId = await productDao.insert(product);
      
      await refresh();
      AppUtils.logInfo('Product created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating product', e);
      return false;
    }
  }

  // Update product
  Future<bool> updateProduct({
    required String id,
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? categoryId,
    String? baseUnitId,
    double? reorderPoint,
    String? imageUrl,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Validate barcode uniqueness if provided and changed
      if (barcode != null && barcode.isNotEmpty) {
        final barcodeExists = await _checkBarcodeExists(barcode, excludeId: id);
        if (barcodeExists) {
          throw Exception('الباركود موجود مسبقاً');
        }
      }
      
      // In a real implementation, you would update in database
      // final success = await productDao.updateProduct(id: id, ...);
      
      await refresh();
      AppUtils.logInfo('Product updated successfully: $id');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating product', e);
      return false;
    }
  }

  // Delete product
  Future<bool> deleteProduct(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // In a real implementation, you would delete from database
      // final success = await productDao.delete(id);
      
      await refresh();
      AppUtils.logInfo('Product deleted successfully: $id');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting product', e);
      return false;
    }
  }

  // Check if barcode exists
  Future<bool> _checkBarcodeExists(String barcode, {String? excludeId}) async {
    // In a real implementation, you would check in database
    // return await productDao.barcodeExists(barcode, excludeId: excludeId);
    return false;
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Product by ID provider
final productByIdProvider = FutureProvider.family<Product?, String>((ref, id) async {
  // In a real implementation, you would fetch from database
  // final productDao = ref.read(productDaoProvider);
  // return await productDao.findById(id);
  return null;
});

// Product by barcode provider
final productByBarcodeProvider = FutureProvider.family<Product?, String>((ref, barcode) async {
  // In a real implementation, you would fetch from database
  // final productDao = ref.read(productDaoProvider);
  // return await productDao.findByBarcode(barcode);
  return null;
});

// Low stock products provider
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  // In a real implementation, you would fetch from database
  // final productDao = ref.read(productDaoProvider);
  // return await productDao.getLowStockProducts();
  return [];
});

// Product validation provider
final productValidationProvider = Provider<ProductValidation>((ref) {
  return ProductValidation(ref);
});

// Product validation class
class ProductValidation {
  final Ref _ref;

  ProductValidation(this._ref);

  // Validate product name (Arabic)
  String? validateNameAr(String nameAr) {
    if (nameAr.trim().isEmpty) {
      return 'اسم المنتج مطلوب';
    }

    if (nameAr.trim().length < 2) {
      return 'اسم المنتج يجب أن يكون حرفين على الأقل';
    }

    if (nameAr.length > 100) {
      return 'اسم المنتج يجب أن يكون أقل من 100 حرف';
    }

    return null;
  }

  // Validate product name (English)
  String? validateNameEn(String? nameEn) {
    if (nameEn != null && nameEn.length > 100) {
      return 'اسم المنتج بالإنجليزية يجب أن يكون أقل من 100 حرف';
    }

    return null;
  }

  // Validate barcode
  Future<String?> validateBarcode(String? barcode, {String? excludeId}) async {
    if (barcode == null || barcode.trim().isEmpty) {
      return null; // Barcode is optional
    }

    if (barcode.length < 8 || barcode.length > 13) {
      return 'الباركود يجب أن يكون بين 8 و 13 رقماً';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(barcode)) {
      return 'الباركود يجب أن يحتوي على أرقام فقط';
    }

    final productManagement = _ref.read(productManagementProvider.notifier);
    final exists = await productManagement._checkBarcodeExists(barcode, excludeId: excludeId);
    
    if (exists) {
      return 'الباركود موجود مسبقاً';
    }

    return null;
  }

  // Validate reorder point
  String? validateReorderPoint(double? reorderPoint) {
    if (reorderPoint != null && reorderPoint < 0) {
      return 'نقطة إعادة الطلب يجب أن تكون صفر أو أكثر';
    }

    return null;
  }

  // Validate complete product data
  Future<Map<String, String?>> validateProduct({
    required String nameAr,
    String? nameEn,
    String? barcode,
    double? reorderPoint,
    String? excludeId,
  }) async {
    final errors = <String, String?>{};

    errors['nameAr'] = validateNameAr(nameAr);
    errors['nameEn'] = validateNameEn(nameEn);
    errors['barcode'] = await validateBarcode(barcode, excludeId: excludeId);
    errors['reorderPoint'] = validateReorderPoint(reorderPoint);

    // Remove null errors
    errors.removeWhere((key, value) => value == null);

    return errors;
  }
}

// Product statistics provider
final productStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  // In a real implementation, you would calculate from database
  return {
    'total': 0,
    'low_stock': 0,
    'out_of_stock': 0,
    'with_barcode': 0,
    'without_barcode': 0,
  };
});
