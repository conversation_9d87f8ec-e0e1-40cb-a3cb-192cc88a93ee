import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'ai_prediction.g.dart';

@JsonSerializable()
class AIPrediction {
  final String id;
  final String type; // sales, inventory, cash_flow, customer_behavior
  final String period; // daily, weekly, monthly, quarterly, yearly
  final DateTime predictionDate;
  final DateTime targetDate;
  final Map<String, dynamic> inputData;
  final Map<String, dynamic> predictions;
  final double confidence;
  final String algorithm;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  const AIPrediction({
    required this.id,
    required this.type,
    required this.period,
    required this.predictionDate,
    required this.targetDate,
    required this.inputData,
    required this.predictions,
    required this.confidence,
    required this.algorithm,
    required this.metadata,
    required this.createdAt,
  });

  factory AIPrediction.fromJson(Map<String, dynamic> json) =>
      _$AIPredictionFromJson(json);
  Map<String, dynamic> toJson() => _$AIPredictionToJson(this);

  factory AIPrediction.fromMap(Map<String, dynamic> map) {
    return AIPrediction(
      id: map[DatabaseConstants.columnAiPredictionId] as String,
      type: map[DatabaseConstants.columnAiPredictionType] as String,
      period: map[DatabaseConstants.columnAiPredictionPeriod] as String,
      predictionDate: DateTime.parse(
          map[DatabaseConstants.columnAiPredictionPredictionDate] as String),
      targetDate: DateTime.parse(
          map[DatabaseConstants.columnAiPredictionTargetDate] as String),
      inputData: Map<String, dynamic>.from(
          map[DatabaseConstants.columnAiPredictionInputData] as Map),
      predictions: Map<String, dynamic>.from(
          map[DatabaseConstants.columnAiPredictionPredictions] as Map),
      confidence: (map[DatabaseConstants.columnAiPredictionConfidence] as num)
          .toDouble(),
      algorithm: map[DatabaseConstants.columnAiPredictionAlgorithm] as String,
      metadata: Map<String, dynamic>.from(
          map[DatabaseConstants.columnAiPredictionMetadata] as Map),
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnAiPredictionCreatedAt] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnAiPredictionId: id,
      DatabaseConstants.columnAiPredictionType: type,
      DatabaseConstants.columnAiPredictionPeriod: period,
      DatabaseConstants.columnAiPredictionPredictionDate:
          predictionDate.toIso8601String(),
      DatabaseConstants.columnAiPredictionTargetDate:
          targetDate.toIso8601String(),
      DatabaseConstants.columnAiPredictionInputData: inputData,
      DatabaseConstants.columnAiPredictionPredictions: predictions,
      DatabaseConstants.columnAiPredictionConfidence: confidence,
      DatabaseConstants.columnAiPredictionAlgorithm: algorithm,
      DatabaseConstants.columnAiPredictionMetadata: metadata,
      DatabaseConstants.columnAiPredictionCreatedAt:
          createdAt.toIso8601String(),
    };
  }

  AIPrediction copyWith({
    String? id,
    String? type,
    String? period,
    DateTime? predictionDate,
    DateTime? targetDate,
    Map<String, dynamic>? inputData,
    Map<String, dynamic>? predictions,
    double? confidence,
    String? algorithm,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return AIPrediction(
      id: id ?? this.id,
      type: type ?? this.type,
      period: period ?? this.period,
      predictionDate: predictionDate ?? this.predictionDate,
      targetDate: targetDate ?? this.targetDate,
      inputData: inputData ?? this.inputData,
      predictions: predictions ?? this.predictions,
      confidence: confidence ?? this.confidence,
      algorithm: algorithm ?? this.algorithm,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIPrediction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AIPrediction(id: $id, type: $type, confidence: $confidence)';
  }

  // Helper methods
  String get displayType {
    switch (type) {
      case PredictionType.sales:
        return 'توقعات المبيعات';
      case PredictionType.inventory:
        return 'توقعات المخزون';
      case PredictionType.cashFlow:
        return 'توقعات التدفق النقدي';
      case PredictionType.customerBehavior:
        return 'توقعات سلوك العملاء';
      case PredictionType.seasonalTrends:
        return 'الاتجاهات الموسمية';
      case PredictionType.demandForecasting:
        return 'توقعات الطلب';
      default:
        return type;
    }
  }

  String get displayPeriod {
    switch (period) {
      case PredictionPeriod.daily:
        return 'يومي';
      case PredictionPeriod.weekly:
        return 'أسبوعي';
      case PredictionPeriod.monthly:
        return 'شهري';
      case PredictionPeriod.quarterly:
        return 'ربع سنوي';
      case PredictionPeriod.yearly:
        return 'سنوي';
      default:
        return period;
    }
  }

  String get confidenceLevel {
    if (confidence >= 0.9) return 'عالية جداً';
    if (confidence >= 0.8) return 'عالية';
    if (confidence >= 0.7) return 'متوسطة';
    if (confidence >= 0.6) return 'منخفضة';
    return 'منخفضة جداً';
  }

  bool get isHighConfidence => confidence >= 0.8;
  bool get isMediumConfidence => confidence >= 0.6 && confidence < 0.8;
  bool get isLowConfidence => confidence < 0.6;
}

@JsonSerializable()
class AIInsight {
  final String id;
  final String category; // performance, opportunity, risk, trend
  final String title;
  final String description;
  final String recommendation;
  final double impact; // 0.0 to 1.0
  final String priority; // high, medium, low
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final DateTime? actionDate;
  final bool isActionable;

  const AIInsight({
    required this.id,
    required this.category,
    required this.title,
    required this.description,
    required this.recommendation,
    required this.impact,
    required this.priority,
    required this.data,
    required this.createdAt,
    this.actionDate,
    this.isActionable = true,
  });

  factory AIInsight.fromJson(Map<String, dynamic> json) =>
      _$AIInsightFromJson(json);
  Map<String, dynamic> toJson() => _$AIInsightToJson(this);

  factory AIInsight.fromMap(Map<String, dynamic> map) {
    return AIInsight(
      id: map['id'] as String,
      category: map['category'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      recommendation: map['recommendation'] as String,
      impact: (map['impact'] as num).toDouble(),
      priority: map['priority'] as String,
      data: Map<String, dynamic>.from(map['data'] as Map),
      createdAt: DateTime.parse(map['created_at'] as String),
      actionDate: map['action_date'] != null
          ? DateTime.parse(map['action_date'] as String)
          : null,
      isActionable: (map['is_actionable'] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category': category,
      'title': title,
      'description': description,
      'recommendation': recommendation,
      'impact': impact,
      'priority': priority,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'action_date': actionDate?.toIso8601String(),
      'is_actionable': isActionable ? 1 : 0,
    };
  }

  String get displayCategory {
    switch (category) {
      case InsightCategory.performance:
        return 'الأداء';
      case InsightCategory.opportunity:
        return 'الفرص';
      case InsightCategory.risk:
        return 'المخاطر';
      case InsightCategory.trend:
        return 'الاتجاهات';
      default:
        return category;
    }
  }

  String get displayPriority {
    switch (priority) {
      case InsightPriority.high:
        return 'عالية';
      case InsightPriority.medium:
        return 'متوسطة';
      case InsightPriority.low:
        return 'منخفضة';
      default:
        return priority;
    }
  }
}

// Prediction types constants
class PredictionType {
  static const String sales = 'sales';
  static const String inventory = 'inventory';
  static const String cashFlow = 'cash_flow';
  static const String customerBehavior = 'customer_behavior';
  static const String seasonalTrends = 'seasonal_trends';
  static const String demandForecasting = 'demand_forecasting';
  static const String profitability = 'profitability';

  static List<String> get allTypes => [
        sales,
        inventory,
        cashFlow,
        customerBehavior,
        seasonalTrends,
        demandForecasting,
        profitability,
      ];
}

// Prediction periods constants
class PredictionPeriod {
  static const String daily = 'daily';
  static const String weekly = 'weekly';
  static const String monthly = 'monthly';
  static const String quarterly = 'quarterly';
  static const String yearly = 'yearly';

  static List<String> get allPeriods => [
        daily,
        weekly,
        monthly,
        quarterly,
        yearly,
      ];
}

// Insight categories constants
class InsightCategory {
  static const String performance = 'performance';
  static const String opportunity = 'opportunity';
  static const String risk = 'risk';
  static const String trend = 'trend';

  static List<String> get allCategories => [
        performance,
        opportunity,
        risk,
        trend,
      ];
}

// Insight priorities constants
class InsightPriority {
  static const String high = 'high';
  static const String medium = 'medium';
  static const String low = 'low';

  static List<String> get allPriorities => [
        high,
        medium,
        low,
      ];
}
