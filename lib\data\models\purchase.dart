import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'purchase.g.dart';

@JsonSerializable()
class Purchase {
  final String id;
  final String? supplierId;
  final String? invoiceNo;
  final String? branchId;
  final DateTime purchaseDate;
  final double totalAmount;
  final double paidAmount;
  final double dueAmount;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Purchase({
    required this.id,
    this.supplierId,
    this.invoiceNo,
    this.branchId,
    required this.purchaseDate,
    required this.totalAmount,
    this.paidAmount = 0,
    this.dueAmount = 0,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  factory Purchase.fromJson(Map<String, dynamic> json) =>
      _$PurchaseFromJson(json);
  Map<String, dynamic> toJson() => _$PurchaseToJson(this);

  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map[DatabaseConstants.columnPurchaseId] as String,
      supplierId: map[DatabaseConstants.columnPurchaseSupplierId] as String?,
      invoiceNo: map[DatabaseConstants.columnPurchaseInvoiceNo] as String?,
      branchId: map[DatabaseConstants.columnPurchaseBranchId] as String?,
      purchaseDate: DateTime.parse(
          map[DatabaseConstants.columnPurchasePurchaseDate] as String),
      totalAmount:
          (map[DatabaseConstants.columnPurchaseTotalAmount] as num).toDouble(),
      paidAmount: (map[DatabaseConstants.columnPurchasePaidAmount] as num?)
              ?.toDouble() ??
          0,
      dueAmount: (map[DatabaseConstants.columnPurchaseDueAmount] as num?)
              ?.toDouble() ??
          0,
      notes: map[DatabaseConstants.columnPurchaseNotes] as String?,
      createdAt: map[DatabaseConstants.columnPurchaseCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnPurchaseUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnPurchaseDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnPurchaseIsSynced] as int?) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnPurchaseId: id,
      DatabaseConstants.columnPurchaseSupplierId: supplierId,
      DatabaseConstants.columnPurchaseInvoiceNo: invoiceNo,
      DatabaseConstants.columnPurchaseBranchId: branchId,
      DatabaseConstants.columnPurchasePurchaseDate:
          purchaseDate.toIso8601String(),
      DatabaseConstants.columnPurchaseTotalAmount: totalAmount,
      DatabaseConstants.columnPurchasePaidAmount: paidAmount,
      DatabaseConstants.columnPurchaseDueAmount: dueAmount,
      DatabaseConstants.columnPurchaseNotes: notes,
      DatabaseConstants.columnPurchaseCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseIsSynced: isSynced ? 1 : 0,
    };
  }

  Purchase copyWith({
    String? id,
    String? supplierId,
    String? invoiceNo,
    String? branchId,
    DateTime? purchaseDate,
    double? totalAmount,
    double? paidAmount,
    double? dueAmount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Purchase(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      branchId: branchId ?? this.branchId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      dueAmount: dueAmount ?? this.dueAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Purchase && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Purchase(id: $id, invoiceNo: $invoiceNo, totalAmount: $totalAmount, purchaseDate: $purchaseDate)';
  }

  // Helper methods
  String get displayInvoiceNo => invoiceNo ?? 'غير محدد';

  String get paymentStatus {
    if (dueAmount <= 0) return 'مدفوع بالكامل';
    if (paidAmount <= 0) return 'غير مدفوع';
    return 'مدفوع جزئياً';
  }

  String get paymentStatusColor {
    if (dueAmount <= 0) return 'green';
    if (paidAmount <= 0) return 'red';
    return 'orange';
  }

  double get paymentPercentage {
    if (totalAmount == 0) return 0;
    return (paidAmount / totalAmount) * 100;
  }

  bool get isFullyPaid => dueAmount <= 0;
  bool get isPartiallyPaid => paidAmount > 0 && dueAmount > 0;
  bool get isUnpaid => paidAmount <= 0;

  String get formattedDate {
    return '${purchaseDate.day}/${purchaseDate.month}/${purchaseDate.year}';
  }

  // Validation methods
  bool get isValid {
    return totalAmount > 0 &&
        paidAmount >= 0 &&
        dueAmount >= 0 &&
        (paidAmount + dueAmount) == totalAmount;
  }

  List<String> get validationErrors {
    final errors = <String>[];

    if (totalAmount <= 0) {
      errors.add('إجمالي المبلغ يجب أن يكون أكبر من صفر');
    }

    if (paidAmount < 0) {
      errors.add('المبلغ المدفوع لا يمكن أن يكون سالباً');
    }

    if (dueAmount < 0) {
      errors.add('المبلغ المستحق لا يمكن أن يكون سالباً');
    }

    if ((paidAmount + dueAmount) != totalAmount) {
      errors.add('مجموع المبلغ المدفوع والمستحق يجب أن يساوي الإجمالي');
    }

    return errors;
  }

  // Factory constructors
  factory Purchase.empty() {
    return Purchase(
      id: '',
      purchaseDate: DateTime.now(),
      totalAmount: 0,
    );
  }

  factory Purchase.create({
    String? supplierId,
    String? invoiceNo,
    String? branchId,
    DateTime? purchaseDate,
    required double totalAmount,
    double paidAmount = 0,
    String? notes,
  }) {
    final dueAmount = totalAmount - paidAmount;

    return Purchase(
      id: '', // Will be generated by DAO
      supplierId: supplierId,
      invoiceNo: invoiceNo,
      branchId: branchId,
      purchaseDate: purchaseDate ?? DateTime.now(),
      totalAmount: totalAmount,
      paidAmount: paidAmount,
      dueAmount: dueAmount,
      notes: notes,
      createdAt: DateTime.now(),
    );
  }
}
