import 'package:sqflite/sqflite.dart' as sql;
import 'package:sqflite/sqlite_api.dart';
import 'package:tijari_tech/data/local/database_constants.dart';
import 'package:uuid/uuid.dart';

import '../local/database.dart';
import '../models/ai_prediction.dart';

class AIPredictionRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  // ------------------------------------------------------------------
  // AIPrediction CRUD Operations
  // ------------------------------------------------------------------

  /// استرجاع جميع التنبؤات
  Future<List<AIPrediction>> getAllPredictions({
    int? limit,
    int? offset,
    String? type,
  }) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (type != null) {
      whereClause = 'WHERE ${DatabaseConstants.columnAiPredictionType} = ?';
      whereArgs.add(type);
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM ${DatabaseConstants.tableAiPredictions} 
      $whereClause
      ORDER BY ${DatabaseConstants.columnAiPredictionCreatedAt} DESC
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''', whereArgs);

    return maps.map((map) => AIPrediction.fromMap(map)).toList();
  }

  /// استرجاع تنبؤ حسب ID
  Future<AIPrediction?> getPredictionById(String id) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionId} = ?',
      whereArgs: [id],
    );
    return maps.isNotEmpty ? AIPrediction.fromMap(maps.first) : null;
  }

  /// إنشاء تنبؤ جديد
  Future<String> createPrediction(AIPrediction prediction) async {
    final sql.Database db = await _databaseHelper.database;
    final String id = prediction.id.isEmpty ? _uuid.v4() : prediction.id;

    final AIPrediction newPrediction = prediction.copyWith(
      id: id,
      createdAt: DateTime.now(),
    );

    await db.insert(
      DatabaseConstants.tableAiPredictions,
      newPrediction.toMap(),
    );
    return id;
  }

  /// تحديث تنبؤ
  Future<void> updatePrediction(AIPrediction prediction) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableAiPredictions,
      prediction.toMap(),
      where: '${DatabaseConstants.columnAiPredictionId} = ?',
      whereArgs: [prediction.id],
    );
  }

  /// حذف تنبؤ
  Future<void> deletePrediction(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.delete(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionId} = ?',
      whereArgs: [id],
    );
  }

  /// استرجاع تنبؤات حسب النوع
  Future<List<AIPrediction>> getPredictionsByType(String type) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionType} = ?',
      whereArgs: [type],
      orderBy: DatabaseConstants.columnAiPredictionCreatedAt,
    );
    return maps.map((map) => AIPrediction.fromMap(map)).toList();
  }

  /// استرجاع تنبؤات حسب الفترة الزمنية
  Future<List<AIPrediction>> getPredictionsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    String? type,
  }) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause =
        '${DatabaseConstants.columnAiPredictionPredictionDate} >= ? AND ${DatabaseConstants.columnAiPredictionPredictionDate} <= ?';
    List<dynamic> whereArgs = [
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ];

    if (type != null) {
      whereClause += ' AND ${DatabaseConstants.columnAiPredictionType} = ?';
      whereArgs.add(type);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableAiPredictions,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: '${DatabaseConstants.columnAiPredictionPredictionDate} DESC',
    );
    return maps.map((map) => AIPrediction.fromMap(map)).toList();
  }

  /// أحدث تنبؤ حسب النوع
  Future<AIPrediction?> getLatestPredictionByType(String type) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionType} = ?',
      whereArgs: [type],
      orderBy: '${DatabaseConstants.columnAiPredictionCreatedAt} DESC',
      limit: 1,
    );
    return maps.isNotEmpty ? AIPrediction.fromMap(maps.first) : null;
  }

  /// تنبؤات عالية الثقة
  Future<List<AIPrediction>> getHighConfidencePredictions({
    double minConfidence = 0.8,
  }) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionConfidence} >= ?',
      whereArgs: [minConfidence],
      orderBy:
          '${DatabaseConstants.columnAiPredictionConfidence} DESC, ${DatabaseConstants.columnAiPredictionCreatedAt} DESC',
    );
    return maps.map((map) => AIPrediction.fromMap(map)).toList();
  }

  /// إحصاءات التنبؤات
  Future<Map<String, dynamic>> getPredictionStatistics() async {
    final sql.Database db = await _databaseHelper.database;

    // إجمالي التنبؤات
    final List<Map<String, dynamic>> totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${DatabaseConstants.tableAiPredictions}');

    // حسب النوع
    final List<Map<String, dynamic>> byTypeResult = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnAiPredictionType}, 
             COUNT(*) as count 
      FROM ${DatabaseConstants.tableAiPredictions} 
      GROUP BY ${DatabaseConstants.columnAiPredictionType}
    ''');

    // متوسط الثقة حسب النوع
    final List<Map<String, dynamic>> avgConfidenceResult = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnAiPredictionType}, 
             AVG(${DatabaseConstants.columnAiPredictionConfidence}) as avg_confidence 
      FROM ${DatabaseConstants.tableAiPredictions} 
      GROUP BY ${DatabaseConstants.columnAiPredictionType}
    ''');

    // الأخيرة (آخر 30 يوم)
    final List<Map<String, dynamic>> recentResult = await db.rawQuery('''
      SELECT COUNT(*) as recent 
      FROM ${DatabaseConstants.tableAiPredictions} 
      WHERE ${DatabaseConstants.columnAiPredictionCreatedAt} > ?
    ''', [DateTime.now().subtract(const Duration(days: 30)).toIso8601String()]);

    final Map<String, int> byType = {};
    for (final row in byTypeResult) {
      byType[row[DatabaseConstants.columnAiPredictionType] as String] =
          row['count'] as int;
    }

    final Map<String, double> avgConfidence = {};
    for (final row in avgConfidenceResult) {
      avgConfidence[row[DatabaseConstants.columnAiPredictionType] as String] =
          (row['avg_confidence'] as num).toDouble();
    }

    return {
      'total': totalResult.first['total'] as int,
      'by_type': byType,
      'average_confidence': avgConfidence,
      'recent': recentResult.first['recent'] as int,
    };
  }

  /// تنظيف التنبؤات القديمة
  Future<int> cleanOldPredictions(int daysOld) async {
    final sql.Database db = await _databaseHelper.database;
    final DateTime cutoffDate =
        DateTime.now().subtract(Duration(days: daysOld));

    return await db.delete(
      DatabaseConstants.tableAiPredictions,
      where: '${DatabaseConstants.columnAiPredictionCreatedAt} < ?',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  // ------------------------------------------------------------------
  // AIInsight CRUD Operations
  // ------------------------------------------------------------------

  /// استرجاع جميع الرؤى
  Future<List<AIInsight>> getAllInsights({
    int? limit,
    String? category,
    String? priority,
  }) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (category != null) {
      whereClause = 'WHERE ${DatabaseConstants.columnAiInsightType} = ?';
      whereArgs.add(category);
    }

    if (priority != null) {
      if (whereClause.isNotEmpty) {
        whereClause += ' AND ${DatabaseConstants.columnAiInsightPriority} = ?';
      } else {
        whereClause = 'WHERE ${DatabaseConstants.columnAiInsightPriority} = ?';
      }
      whereArgs.add(priority);
    }
// يراجع
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM ai_insights 
      $whereClause
      ORDER BY 
        CASE ${DatabaseConstants.columnAiInsightPriority} 
          WHEN 'high' THEN 3 
          WHEN 'medium' THEN 2 
          WHEN 'low' THEN 1 
          ELSE 0 
        END DESC,
        ${DatabaseConstants.columnAiInsightStatus} DESC,
    
        ${DatabaseConstants.columnAiInsightCreatedAt} DESC
      ${limit != null ? 'LIMIT $limit' : ''}
    ''', whereArgs);

    return maps.map((map) => AIInsight.fromMap(map)).toList();
  }

  /// إنشاء رؤية جديدة
  Future<String> createInsight(AIInsight insight) async {
    final sql.Database db = await _databaseHelper.database;
    final String id = insight.id.isEmpty ? _uuid.v4() : insight.id;

    final AIInsight newInsight = AIInsight(
      id: id,
      category: insight.category,
      title: insight.title,
      description: insight.description,
      recommendation: insight.recommendation,
      impact: insight.impact,
      priority: insight.priority,
      data: insight.data,
      createdAt: DateTime.now(),
      actionDate: insight.actionDate,
      isActionable: insight.isActionable,
    );

    await db.insert('ai_insights', newInsight.toMap());
    return id;
  }

  /// تحديث رؤية
  Future<void> updateInsight(AIInsight insight) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      'ai_insights',
      insight.toMap(),
      where: 'id = ?',
      whereArgs: [insight.id],
    );
  }

  /// حذف رؤية
  Future<void> deleteInsight(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.delete(
      'ai_insights',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// استرجاع رؤى حسب الفئة
  Future<List<AIInsight>> getInsightsByCategory(String category) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ai_insights',
      where: '${DatabaseConstants.columnAiInsightType} = ?',
      whereArgs: [category],
      orderBy:
          '${DatabaseConstants.columnAiInsightStatus} DESC, ${DatabaseConstants.columnAiInsightCreatedAt} DESC',
    );
    // يراجع columnAiInsightStatus / 
    return maps.map((map) => AIInsight.fromMap(map)).toList();
  }

  /// رؤى قابلة للتنفيذ
  Future<List<AIInsight>> getActionableInsights() async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'ai_insights',
      where: 'is_actionable = 1',
      orderBy: '''
        CASE ${DatabaseConstants.columnAiInsightPriority} 
          WHEN 'high' THEN 3 
          WHEN 'medium' THEN 2 
          WHEN 'low' THEN 1 
          ELSE 0 
        END DESC,
        ${DatabaseConstants.columnAiInsightStatus} DESC
      ''',
    );
    //     // يراجع columnAiInsightStatus / columnAiInsightImpact

    return maps.map((map) => AIInsight.fromMap(map)).toList();
  }

  /// إحصاءات الرؤى
  Future<Map<String, dynamic>> getInsightStatistics() async {
    final sql.Database db = await _databaseHelper.database;

    // إجمالي الرؤى
    final List<Map<String, dynamic>> totalResult =
        await db.rawQuery('SELECT COUNT(*) as total FROM ai_insights');

    // حسب الفئة
    final List<Map<String, dynamic>> byCategoryResult = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnAiInsightType}, 
             COUNT(*) as count 
      FROM ai_insights 
      GROUP BY ${DatabaseConstants.columnAiInsightType}
    ''');

    // حسب الأولوية
    final List<Map<String, dynamic>> byPriorityResult = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnAiInsightPriority}, 
             COUNT(*) as count 
      FROM ai_insights 
      GROUP BY ${DatabaseConstants.columnAiInsightPriority}
    ''');

    // الرؤى القابلة للتنفيذ
    final List<Map<String, dynamic>> actionableResult = await db.rawQuery(
        'SELECT COUNT(*) as actionable FROM ai_insights WHERE is_actionable = 1');

    final Map<String, int> byCategory = {};
    for (final row in byCategoryResult) {
      byCategory[row[DatabaseConstants.columnAiInsightType] as String] =
          row['count'] as int;
    }

    final Map<String, int> byPriority = {};
    for (final row in byPriorityResult) {
      byPriority[row[DatabaseConstants.columnAiInsightPriority] as String] =
          row['count'] as int;
    }

    return {
      'total': totalResult.first['total'] as int,
      'by_category': byCategory,
      'by_priority': byPriority,
      'actionable': actionableResult.first['actionable'] as int,
    };
  }

  // ------------------------------------------------------------------
  // Batch Operations
  // ------------------------------------------------------------------

  /// إنشاء تنبؤات بالجملة
  Future<void> createMultiplePredictions(List<AIPrediction> predictions) async {
    final sql.Database db = await _databaseHelper.database;
    final Batch batch = db.batch();

    for (final prediction in predictions) {
      final String id = prediction.id.isEmpty ? _uuid.v4() : prediction.id;
      final AIPrediction newPrediction = prediction.copyWith(
        id: id,
        createdAt: DateTime.now(),
      );
      batch.insert(DatabaseConstants.tableAiPredictions, newPrediction.toMap());
    }

    await batch.commit();
  }

  /// إنشاء رؤى بالجملة
  Future<void> createMultipleInsights(List<AIInsight> insights) async {
    final sql.Database db = await _databaseHelper.database;
    final Batch batch = db.batch();

    for (final insight in insights) {
      final String id = insight.id.isEmpty ? _uuid.v4() : insight.id;
      final AIInsight newInsight = AIInsight(
        id: id,
        category: insight.category,
        title: insight.title,
        description: insight.description,
        recommendation: insight.recommendation,
        impact: insight.impact,
        priority: insight.priority,
        data: insight.data,
        createdAt: DateTime.now(),
        actionDate: insight.actionDate,
        isActionable: insight.isActionable,
      );
      batch.insert('ai_insights', newInsight.toMap());
    }

    await batch.commit();
  }
}