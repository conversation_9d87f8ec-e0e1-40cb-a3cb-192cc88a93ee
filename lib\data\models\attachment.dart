import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'attachment.g.dart';

@JsonSerializable()
class Attachment {
  final String id;
  final String referenceType; // sale, purchase, product, customer, etc.
  final String referenceId;
  final String fileName;
  final String filePath;
  final String? mimeType;
  final int? size;
  final String? uploadedBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Attachment({
    required this.id,
    required this.referenceType,
    required this.referenceId,
    required this.fileName,
    required this.filePath,
    this.mimeType,
    this.size,
    this.uploadedBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory Attachment.fromJson(Map<String, dynamic> json) =>
      _$AttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$AttachmentToJson(this);

  factory Attachment.fromMap(Map<String, dynamic> map) {
    return Attachment(
      id: map[DatabaseConstants.columnAttachmentId] as String,
      referenceType:
          map[DatabaseConstants.columnAttachmentReferenceType] as String,
      referenceId: map[DatabaseConstants.columnAttachmentReferenceId] as String,
      fileName: map[DatabaseConstants.columnAttachmentFileName] as String,
      filePath: map[DatabaseConstants.columnAttachmentFilePath] as String,
      mimeType: map[DatabaseConstants.columnAttachmentMimeType] as String?,
      size: map[DatabaseConstants.columnAttachmentSize] as int?,
      uploadedBy: map[DatabaseConstants.columnAttachmentUploadedBy] as String?,
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnAttachmentCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnAttachmentUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnAttachmentUpdatedAt] as String)
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnAttachmentId: id,
      DatabaseConstants.columnAttachmentReferenceType: referenceType,
      DatabaseConstants.columnAttachmentReferenceId: referenceId,
      DatabaseConstants.columnAttachmentFileName: fileName,
      DatabaseConstants.columnAttachmentFilePath: filePath,
      DatabaseConstants.columnAttachmentMimeType: mimeType,
      DatabaseConstants.columnAttachmentSize: size,
      DatabaseConstants.columnAttachmentUploadedBy: uploadedBy,
      DatabaseConstants.columnAttachmentCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnAttachmentUpdatedAt: updatedAt?.toIso8601String(),
    };
  }

  Attachment copyWith({
    String? id,
    String? referenceType,
    String? referenceId,
    String? fileName,
    String? filePath,
    String? mimeType,
    int? size,
    String? uploadedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Attachment(
      id: id ?? this.id,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attachment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Attachment(id: $id, fileName: $fileName, referenceType: $referenceType)';
  }

  // Helper methods
  String get displayReferenceType {
    switch (referenceType) {
      case AttachmentReferenceType.sale:
        return 'فاتورة مبيعات';
      case AttachmentReferenceType.purchase:
        return 'فاتورة مشتريات';
      case AttachmentReferenceType.product:
        return 'منتج';
      case AttachmentReferenceType.customer:
        return 'عميل';
      case AttachmentReferenceType.supplier:
        return 'مورد';
      case AttachmentReferenceType.stockAdjustment:
        return 'تسوية مخزون';
      case AttachmentReferenceType.transaction:
        return 'معاملة مالية';
      default:
        return referenceType;
    }
  }

  String get fileExtension {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  String get displaySize {
    if (size == null) return 'غير معروف';

    if (size! < 1024) {
      return '$size بايت';
    } else if (size! < 1024 * 1024) {
      return '${(size! / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(size! / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  bool get isImage {
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.contains(fileExtension);
  }

  bool get isPdf {
    return fileExtension == 'pdf';
  }

  bool get isDocument {
    final docExtensions = ['doc', 'docx', 'txt', 'rtf'];
    return docExtensions.contains(fileExtension);
  }

  bool get isSpreadsheet {
    final spreadsheetExtensions = ['xls', 'xlsx', 'csv'];
    return spreadsheetExtensions.contains(fileExtension);
  }
}

// Attachment reference types constants
class AttachmentReferenceType {
  static const String sale = 'sale';
  static const String purchase = 'purchase';
  static const String product = 'product';
  static const String customer = 'customer';
  static const String supplier = 'supplier';
  static const String stockAdjustment = 'stock_adjustment';
  static const String transaction = 'transaction';
  static const String user = 'user';

  static List<String> get allTypes => [
        sale,
        purchase,
        product,
        customer,
        supplier,
        stockAdjustment,
        transaction,
        user,
      ];
}
