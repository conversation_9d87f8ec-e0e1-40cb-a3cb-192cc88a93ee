import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';
import '../../widgets/dashboard_widgets.dart';

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'لوحة التحكم',
      child: ResponsiveBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome section
                _buildWelcomeSection(context),
                SizedBox(height: 24.h),

                // Statistics cards
                _buildStatisticsSection(context, constraints),
                SizedBox(height: 24.h),

                // Charts section
                _buildChartsSection(context, constraints),
                SizedBox(height: 24.h),

                // Quick actions
                _buildQuickActionsSection(context, constraints),
                SizedBox(height: 24.h),

                // Recent activities
                _buildRecentActivitiesSection(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك في تجاري تك',
            style: AppTextStyles.headlineMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'نظام إدارة شامل لأعمالك التجارية',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Colors.white70,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            DateTime.now().toDisplayDate,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white60,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection(
      BuildContext context, BoxConstraints constraints) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: context.isMobile ? 2 : 4,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: 0.7,
          children: [
            StatisticsCard(
              title: 'إجمالي المبيعات',
              value: '21,001,585.06',
              subtitle: 'ر.س',
              icon: Icons.trending_up,
              color: AppColors.success,
              backgroundColor: AppColors.success.withValues(alpha: 0.1),
            ),
            StatisticsCard(
              title: 'إجمالي المشتريات',
              value: '17,797,161.74',
              subtitle: 'ر.س',
              icon: Icons.shopping_cart,
              color: AppColors.info,
              backgroundColor: AppColors.info.withValues(alpha: 0.1),
            ),
            StatisticsCard(
              title: 'قيمة المخزون',
              value: '1,324,717.60',
              subtitle: 'ر.س',
              icon: Icons.inventory_2,
              color: AppColors.warning,
              backgroundColor: AppColors.warning.withValues(alpha: 0.1),
            ),
            StatisticsCard(
              title: 'نسبة الربح',
              value: '18.1%',
              subtitle: 'ربح',
              icon: Icons.percent,
              color: AppColors.primary,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsSection(BuildContext context, BoxConstraints constraints) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرسوم البيانية',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        if (constraints.maxWidth >= 1024)
          Row(
            children: [
              Expanded(
                child: SalesChart(
                  title: 'المبيعات الشهرية',
                  data: _getSampleSalesData(),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CategoryPieChart(
                  title: 'المبيعات حسب الفئة',
                  data: _getSampleCategoryData(),
                ),
              ),
            ],
          )
        else
          Column(
            children: [
              SalesChart(
                title: 'المبيعات الشهرية',
                data: _getSampleSalesData(),
              ),
              SizedBox(height: 16.h),
              CategoryPieChart(
                title: 'المبيعات حسب الفئة',
                data: _getSampleCategoryData(),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildQuickActionsSection(
      BuildContext context, BoxConstraints constraints) {
    final actions = [
      QuickAction(
        title: 'نقطة البيع',
        icon: Icons.point_of_sale,
        color: AppColors.primary,
        onTap: () => context.go(AppRoutes.pos),
      ),
      QuickAction(
        title: 'إضافة منتج',
        icon: Icons.add_box_outlined,
        color: AppColors.success,
        onTap: () => context.go(AppRoutes.productAdd),
      ),
      QuickAction(
        title: 'فاتورة شراء',
        icon: Icons.receipt_long_outlined,
        color: AppColors.info,
        onTap: () => context.go(AppRoutes.purchaseAdd),
      ),
      QuickAction(
        title: 'إضافة عميل',
        icon: Icons.person_add_outlined,
        color: AppColors.warning,
        onTap: () => context.go(AppRoutes.customerAdd),
      ),
      QuickAction(
        title: 'التقارير',
        icon: Icons.analytics_outlined,
        color: AppColors.accent,
        onTap: () => context.go(AppRoutes.reports),
      ),
      QuickAction(
        title: 'المزامنة',
        icon: Icons.sync_outlined,
        color: AppColors.secondary,
        onTap: () {
          context.showInfoSnackBar('جاري المزامنة...');
        },
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        QuickActionsGrid(actions: actions),
      ],
    );
  }

  Widget _buildRecentActivitiesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النشاطات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to full activity log
              },
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.receipt_outlined,
                    color: AppColors.primary,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  'فاتورة مبيعات #INV00012${index + 1}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'عميل: أحمد محمد - ${(1250.0 * (index + 1)).toCurrency}',
                  style: AppTextStyles.bodySmall,
                ),
                trailing: Text(
                  '${index + 1} دقيقة',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                onTap: () {
                  // TODO: Navigate to invoice details
                },
              );
            },
          ),
        ),
      ],
    );
  }

  List<SalesData> _getSampleSalesData() {
    return [
      SalesData(label: 'يناير', value: 15000, color: AppColors.primary),
      SalesData(label: 'فبراير', value: 18000, color: AppColors.primary),
      SalesData(label: 'مارس', value: 22000, color: AppColors.primary),
      SalesData(label: 'أبريل', value: 19000, color: AppColors.primary),
      SalesData(label: 'مايو', value: 25000, color: AppColors.primary),
      SalesData(label: 'يونيو', value: 28000, color: AppColors.primary),
    ];
  }

  List<CategoryData> _getSampleCategoryData() {
    return [
      CategoryData(
        label: 'إلكترونيات',
        value: 35,
        percentage: 35,
        color: AppColors.primary,
      ),
      CategoryData(
        label: 'ملابس',
        value: 25,
        percentage: 25,
        color: AppColors.success,
      ),
      CategoryData(
        label: 'أغذية',
        value: 20,
        percentage: 20,
        color: AppColors.warning,
      ),
      CategoryData(
        label: 'أخرى',
        value: 20,
        percentage: 20,
        color: AppColors.info,
      ),
    ];
  }
}
