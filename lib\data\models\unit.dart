import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'unit.g.dart';

@JsonSerializable()
class Unit {
  final String id;
  final String name;
  final double factor; // معامل التحويل إلى الوحدة الأساسية
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Unit({
    required this.id,
    required this.name,
    required this.factor,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new Unit instance from a map
  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map[DatabaseConstants.columnUnitId] as String,
      name: map[DatabaseConstants.columnUnitName] as String,
      factor: (map[DatabaseConstants.columnUnitFactor] as num).toDouble(),
      createdAt: map[DatabaseConstants.columnUnitCreatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnUnitUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnUnitDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnUnitIsSynced] as int? ?? 0) == 1,
    );
  }

  // Convert Unit instance to a map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnUnitId: id,
      DatabaseConstants.columnUnitName: name,
      DatabaseConstants.columnUnitFactor: factor,
      DatabaseConstants.columnUnitCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnUnitUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnUnitDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnUnitIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Unit.fromJson(Map<String, dynamic> json) => _$UnitFromJson(json);
  Map<String, dynamic> toJson() => _$UnitToJson(this);

  // Copy with method for creating modified copies
  Unit copyWith({
    String? id,
    String? name,
    double? factor,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Unit(
      id: id ?? this.id,
      name: name ?? this.name,
      factor: factor ?? this.factor,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Unit && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Unit(id: $id, name: $name, factor: $factor, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get isBaseUnit => factor == 1.0;

  // Convert quantity from this unit to base unit
  double toBaseUnit(double quantity) {
    return quantity * factor;
  }

  // Convert quantity from base unit to this unit
  double fromBaseUnit(double baseQuantity) {
    return baseQuantity / factor;
  }

  // Convert quantity from this unit to another unit
  double convertTo(double quantity, Unit targetUnit) {
    final baseQuantity = toBaseUnit(quantity);
    return targetUnit.fromBaseUnit(baseQuantity);
  }

  // Create a new unit with current timestamp
  static Unit create({
    required String id,
    required String name,
    required double factor,
  }) {
    final now = DateTime.now();
    return Unit(
      id: id,
      name: name,
      factor: factor,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update unit with new timestamp
  Unit update({
    String? name,
    double? factor,
  }) {
    return copyWith(
      name: name ?? this.name,
      factor: factor ?? this.factor,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Unit markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Unit markAsSynced() {
    return copyWith(isSynced: true);
  }

  // Common unit factory methods
  static Unit piece({required String id}) => Unit.create(
        id: id,
        name: 'قطعة',
        factor: 1.0,
      );

  static Unit box({required String id, required double piecesPerBox}) =>
      Unit.create(
        id: id,
        name: 'كرتون',
        factor: piecesPerBox,
      );

  static Unit pack({required String id, required double piecesPerPack}) =>
      Unit.create(
        id: id,
        name: 'باكت',
        factor: piecesPerPack,
      );

  static Unit kilogram({required String id}) => Unit.create(
        id: id,
        name: 'كيلوجرام',
        factor: 1000.0, // assuming base unit is gram
      );

  static Unit gram({required String id}) => Unit.create(
        id: id,
        name: 'جرام',
        factor: 1.0,
      );

  static Unit liter({required String id}) => Unit.create(
        id: id,
        name: 'لتر',
        factor: 1000.0, // assuming base unit is milliliter
      );

  static Unit milliliter({required String id}) => Unit.create(
        id: id,
        name: 'مليلتر',
        factor: 1.0,
      );
}
