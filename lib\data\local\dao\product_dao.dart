import 'package:tijari_tech/data/local/database_constants.dart';
import 'base_dao.dart';
import '../../models/product.dart';
import '../../../core/utils/app_utils.dart';

class ProductDao extends BaseDao<Product> {
  @override
  String get tableName => DatabaseConstants.tableProducts;

  @override
  Product fromMap(Map<String, dynamic> map) => Product.fromMap(map);

  @override
  Map<String, dynamic> toMap(Product entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Product-specific queries
  // ------------------------------------------------------------------

  /// استرجاع المنتجات حسب الفئة
  Future<List<Product>> findByCategory(String categoryId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnProductCategoryId} = ?',
      whereArgs: [categoryId],
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع منتج حسب الباركود
  Future<Product?> findByBarcode(String barcode) async {
    final products = await findWhere(
      where: '${DatabaseConstants.columnProductBarcode} = ?',
      whereArgs: [barcode],
      limit: 1,
    );
    return products.isNotEmpty ? products.first : null;
  }

  /// البحث عن المنتجات حسب الاسم
  Future<List<Product>> searchByName(String searchTerm) async {
    return await findWhere(
      where: '${DatabaseConstants.columnProductNameAr} LIKE ? OR ${DatabaseConstants.columnProductNameEn} LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع المنتجات النشطة فقط
  Future<List<Product>> getActiveProducts() async {
    return await findWhere(
      where: '${DatabaseConstants.columnProductIsActive} = 1',
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع المنتجات ذات المخزون المنخفض
  Future<List<Map<String, dynamic>>> getProductsWithLowStock() async {
    final sql = '''
      SELECT 
        p.*, 
        s.${DatabaseConstants.columnStockQuantity} as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL 
        AND p.${DatabaseConstants.columnProductTrackStock} = 1
        AND (s.${DatabaseConstants.columnStockQuantity} <= p.${DatabaseConstants.columnProductMinStock} 
             OR s.${DatabaseConstants.columnStockQuantity} IS NULL)
      ORDER BY p.${DatabaseConstants.columnProductNameAr}
    ''';
    return await rawQuery(sql);
  }

  /// استرجاع المنتجات مع معلومات المخزون
  Future<List<Map<String, dynamic>>> getProductsWithStock(
      {String? warehouseId}) async {
    String sql = '''
      SELECT 
        p.*,
        s.${DatabaseConstants.columnStockQuantity} as current_stock,
        w.${DatabaseConstants.columnWarehouseName} as warehouse_name,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c 
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    ''';

    List<dynamic> args = [];
    if (warehouseId != null) {
      sql += ' AND s.${DatabaseConstants.columnStockWarehouseId} = ?';
      args.add(warehouseId);
    }

    sql += ' ORDER BY p.${DatabaseConstants.columnProductNameAr}';
    return await rawQuery(sql, args.isNotEmpty ? args : null);
  }

  /// استرجاع إحصاءات مبيعات المنتج
  Future<Map<String, dynamic>?> getProductSalesStats(String productId) async {
    final sql = '''
      SELECT 
        p.${DatabaseConstants.columnProductId},
        p.${DatabaseConstants.columnProductNameAr},
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}), 0) as total_revenue,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemSaleId}) as sales_count,
        AVG(si.${DatabaseConstants.columnSaleItemUnitPrice}) as avg_price
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} s 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = s.${DatabaseConstants.columnSaleId}
      WHERE p.${DatabaseConstants.columnProductId} = ? 
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
        AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}, p.${DatabaseConstants.columnProductNameAr}
    ''';

    final result = await rawQuery(sql, [productId]);
    return result.isNotEmpty ? result.first : null;
  }

  /// تحديث مخزون المنتج
  Future<bool> updateStock(
      String productId, String warehouseId, double quantity) async {
    try {
      final db = await database;

      final existingStock = await db.query(
        DatabaseConstants.tableStocks,
        where:
            '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
        whereArgs: [productId, warehouseId],
        limit: 1,
      );

      final now = DateTime.now().toIso8601String();

      if (existingStock.isNotEmpty) {
        await db.update(
          DatabaseConstants.tableStocks,
          {
            DatabaseConstants.columnStockQuantity: quantity,
            DatabaseConstants.columnStockUpdatedAt: now,
            DatabaseConstants.columnStockIsSynced: 0,
          },
          where:
              '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
          whereArgs: [productId, warehouseId],
        );
      } else {
        await db.insert(DatabaseConstants.tableStocks, {
          DatabaseConstants.columnStockId: AppUtils.generateId(),
          DatabaseConstants.columnStockProductId: productId,
          DatabaseConstants.columnStockWarehouseId: warehouseId,
          DatabaseConstants.columnStockQuantity: quantity,
          DatabaseConstants.columnStockCreatedAt: now,
          DatabaseConstants.columnStockUpdatedAt: now,
          DatabaseConstants.columnStockIsSynced: 0,
        });
      }
      return true;
    } catch (e) {
      AppUtils.logError('Error updating product stock', e);
      return false;
    }
  }

  /// استرجاع المخزون الحالي
  Future<double> getCurrentStock(String productId, String warehouseId) async {
    try {
      final db = await database;
      final result = await db.query(
        DatabaseConstants.tableStocks,
        columns: [DatabaseConstants.columnStockQuantity],
        where:
            '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
        whereArgs: [productId, warehouseId],
        limit: 1,
      );

      return result.isNotEmpty
          ? (result.first[DatabaseConstants.columnStockQuantity] as num)
              .toDouble()
          : 0.0;
    } catch (e) {
      AppUtils.logError('Error getting current stock', e);
      return 0.0;
    }
  }

  /// التحقق من تفرّد الباركود
  Future<bool> barcodeExists(String barcode, {String? excludeProductId}) async {
    try {
      String where =
          '${DatabaseConstants.columnProductBarcode} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL';
      List<dynamic> whereArgs = [barcode];

      if (excludeProductId != null) {
        where += ' AND ${DatabaseConstants.columnProductId} != ?';
        whereArgs.add(excludeProductId);
      }

      final count = await this.count(where: where, whereArgs: whereArgs);
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error checking barcode existence', e);
      return false;
    }
  }

  /// استرجاع المنتجات حسب نطاق السعر
  Future<List<Product>> getProductsByPriceRange(
      double minPrice, double maxPrice) async {
    return await findWhere(
      where:
          '${DatabaseConstants.columnProductSellingPrice} >= ? AND ${DatabaseConstants.columnProductSellingPrice} <= ?',
      whereArgs: [minPrice, maxPrice],
      orderBy: DatabaseConstants.columnProductSellingPrice,
    );
  }

  /// استرجاع أكثر المنتجات مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingProducts(
      {int limit = 10}) async {
    final sql = '''
      SELECT 
        p.*,
        SUM(si.${DatabaseConstants.columnSaleItemQty}) as total_sold,
        SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}) as total_revenue
      FROM ${DatabaseConstants.tableProducts} p
      JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      JOIN ${DatabaseConstants.tableSales} s 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = s.${DatabaseConstants.columnSaleId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL 
        AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}
      ORDER BY total_sold DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  /// استرجاع المنتجات الراكدة
  Future<List<Map<String, dynamic>>> getSlowMovingProducts(
      {int daysThreshold = 30}) async {
    final cutoffDate =
        DateTime.now().subtract(Duration(days: daysThreshold));

    final sql = '''
      SELECT 
        p.*,
        s.${DatabaseConstants.columnStockQuantity} as current_stock,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} sal 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = sal.${DatabaseConstants.columnSaleSaleDate}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}
      HAVING total_sold <= 5 OR total_sold IS NULL
      ORDER BY total_sold ASC
    ''';

    return await rawQuery(sql, [cutoffDate.toIso8601String()]);
  }

  /// تحديث الأسعار بالجملة
  Future<bool> bulkUpdatePrices(
      Map<String, Map<String, double>> priceUpdates) async {
    try {
      final db = await database;
      final batch = db.batch();
      final now = DateTime.now().toIso8601String();

      priceUpdates.forEach((productId, prices) {
        final updateData = <String, dynamic>{
          DatabaseConstants.columnProductUpdatedAt: now,
          DatabaseConstants.columnProductIsSynced: 0,
        };

        if (prices.containsKey(DatabaseConstants.columnProductCostPrice)) {
          updateData[DatabaseConstants.columnProductCostPrice] =
              prices[DatabaseConstants.columnProductCostPrice];
        }
        if (prices.containsKey(DatabaseConstants.columnProductSellingPrice)) {
          updateData[DatabaseConstants.columnProductSellingPrice] =
              prices[DatabaseConstants.columnProductSellingPrice];
        }

        batch.update(
          tableName,
          updateData,
          where: '${DatabaseConstants.columnProductId} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
          whereArgs: [productId],
        );
      });

      await batch.commit(noResult: true);
      AppUtils.logInfo('Bulk updated prices for ${priceUpdates.length} products');
      return true;
    } catch (e) {
      AppUtils.logError('Error bulk updating prices', e);
      return false;
    }
  }
}