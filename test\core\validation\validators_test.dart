import 'package:flutter_test/flutter_test.dart';
import 'package:tijari_tech/core/validation/validators.dart';

void main() {
  group('Validators Tests', () {
    group('RequiredValidator', () {
      late RequiredValidator validator;

      setUp(() {
        validator = RequiredValidator();
      });

      test('should return error for null value', () {
        final result = validator.validate(null);
        expect(result, isNotNull);
        expect(result, equals('هذا الحقل مطلوب'));
      });

      test('should return error for empty string', () {
        final result = validator.validate('');
        expect(result, isNotNull);
        expect(result, equals('هذا الحقل مطلوب'));
      });

      test('should return error for whitespace only', () {
        final result = validator.validate('   ');
        expect(result, isNotNull);
        expect(result, equals('هذا الحقل مطلوب'));
      });

      test('should return null for valid value', () {
        final result = validator.validate('test');
        expect(result, isNull);
      });

      test('should use custom message', () {
        final customValidator = RequiredValidator(message: 'Custom message');
        final result = customValidator.validate(null);
        expect(result, equals('Custom message'));
      });
    });

    group('MinLengthValidator', () {
      late MinLengthValidator validator;

      setUp(() {
        validator = MinLengthValidator(5);
      });

      test('should return error for short string', () {
        final result = validator.validate('test');
        expect(result, isNotNull);
        expect(result, contains('5'));
      });

      test('should return error for null value', () {
        final result = validator.validate(null);
        expect(result, isNotNull);
      });

      test('should return null for valid length', () {
        final result = validator.validate('testing');
        expect(result, isNull);
      });

      test('should return null for exact length', () {
        final result = validator.validate('tests');
        expect(result, isNull);
      });
    });

    group('MaxLengthValidator', () {
      late MaxLengthValidator validator;

      setUp(() {
        validator = MaxLengthValidator(5);
      });

      test('should return error for long string', () {
        final result = validator.validate('testing');
        expect(result, isNotNull);
        expect(result, contains('5'));
      });

      test('should return null for null value', () {
        final result = validator.validate(null);
        expect(result, isNull);
      });

      test('should return null for valid length', () {
        final result = validator.validate('test');
        expect(result, isNull);
      });

      test('should return null for exact length', () {
        final result = validator.validate('tests');
        expect(result, isNull);
      });
    });

    group('EmailValidator', () {
      late EmailValidator validator;

      setUp(() {
        validator = EmailValidator();
      });

      test('should return null for valid emails', () {
        final validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in validEmails) {
          final result = validator.validate(email);
          expect(result, isNull, reason: 'Failed for email: $email');
        }
      });

      test('should return error for invalid emails', () {
        final invalidEmails = [
          'invalid-email',
          'test@',
          '@example.com',
          '<EMAIL>',
          'test@example',
          'test@.com',
        ];

        for (final email in invalidEmails) {
          final result = validator.validate(email);
          expect(result, isNotNull, reason: 'Should fail for email: $email');
        }
      });

      test('should return null for empty value', () {
        final result = validator.validate('');
        expect(result, isNull);
      });

      test('should return null for null value', () {
        final result = validator.validate(null);
        expect(result, isNull);
      });
    });

    group('PhoneValidator', () {
      late PhoneValidator validator;

      setUp(() {
        validator = PhoneValidator();
      });

      test('should return null for valid phone numbers', () {
        final validPhones = [
          '0501234567',
          '966501234567',
          '+966501234567',
          '05-0123-4567',
          '966 50 123 4567',
        ];

        for (final phone in validPhones) {
          final result = validator.validate(phone);
          expect(result, isNull, reason: 'Failed for phone: $phone');
        }
      });

      test('should return error for invalid phone numbers', () {
        final invalidPhones = [
          '123',
          'invalid',
          '12345678901234567890',
          'abcd1234567',
        ];

        for (final phone in invalidPhones) {
          final result = validator.validate(phone);
          expect(result, isNotNull, reason: 'Should fail for phone: $phone');
        }
      });

      test('should return null for empty value', () {
        final result = validator.validate('');
        expect(result, isNull);
      });
    });

    group('NumericValidator', () {
      late NumericValidator validator;

      setUp(() {
        validator = NumericValidator();
      });

      test('should return null for valid numbers', () {
        final validNumbers = [
          '123',
          '123.45',
          '0',
          '0.0',
          '-123',
          '-123.45',
        ];

        for (final number in validNumbers) {
          final result = validator.validate(number);
          expect(result, isNull, reason: 'Failed for number: $number');
        }
      });

      test('should return error for invalid numbers', () {
        final invalidNumbers = [
          'abc',
          '123abc',
          'abc123',
          '12.34.56',
          '',
        ];

        for (final number in invalidNumbers) {
          final result = validator.validate(number);
          expect(result, isNotNull, reason: 'Should fail for number: $number');
        }
      });

      test('should return null for null value', () {
        final result = validator.validate(null);
        expect(result, isNull);
      });
    });

    group('MinValueValidator', () {
      late MinValueValidator validator;

      setUp(() {
        validator = MinValueValidator(10);
      });

      test('should return error for values below minimum', () {
        final result = validator.validate('5');
        expect(result, isNotNull);
        expect(result, contains('10'));
      });

      test('should return null for values above minimum', () {
        final result = validator.validate('15');
        expect(result, isNull);
      });

      test('should return null for exact minimum value', () {
        final result = validator.validate('10');
        expect(result, isNull);
      });

      test('should return error for non-numeric values', () {
        final result = validator.validate('abc');
        expect(result, isNotNull);
      });
    });

    group('MaxValueValidator', () {
      late MaxValueValidator validator;

      setUp(() {
        validator = MaxValueValidator(100);
      });

      test('should return error for values above maximum', () {
        final result = validator.validate('150');
        expect(result, isNotNull);
        expect(result, contains('100'));
      });

      test('should return null for values below maximum', () {
        final result = validator.validate('50');
        expect(result, isNull);
      });

      test('should return null for exact maximum value', () {
        final result = validator.validate('100');
        expect(result, isNull);
      });
    });

    group('BarcodeValidator', () {
      late BarcodeValidator validator;

      setUp(() {
        validator = BarcodeValidator();
      });

      test('should return null for valid barcodes', () {
        final validBarcodes = [
          '12345678',
          '1234567890123',
          '123456789012',
        ];

        for (final barcode in validBarcodes) {
          final result = validator.validate(barcode);
          expect(result, isNull, reason: 'Failed for barcode: $barcode');
        }
      });

      test('should return error for invalid barcodes', () {
        final invalidBarcodes = [
          '123',
          '12345678901234',
          'abc12345678',
          '123-456-789',
        ];

        for (final barcode in invalidBarcodes) {
          final result = validator.validate(barcode);
          expect(result, isNotNull, reason: 'Should fail for barcode: $barcode');
        }
      });
    });

    group('CompositeValidator', () {
      test('should validate with multiple validators', () {
        final validator = CompositeValidator([
          RequiredValidator(),
          MinLengthValidator(5),
          MaxLengthValidator(10),
        ]);

        expect(validator.validate(null), isNotNull);
        expect(validator.validate(''), isNotNull);
        expect(validator.validate('test'), isNotNull); // Too short
        expect(validator.validate('testing too long'), isNotNull); // Too long
        expect(validator.validate('testing'), isNull); // Valid
      });

      test('should return first error encountered', () {
        final validator = CompositeValidator([
          RequiredValidator(message: 'Required error'),
          MinLengthValidator(5, message: 'Min length error'),
        ]);

        final result = validator.validate('');
        expect(result, equals('Required error'));
      });
    });

    group('ValidationHelper', () {
      test('required should create RequiredValidator', () {
        final validator = ValidationHelper.required();
        expect(validator.validate(null), isNotNull);
        expect(validator.validate('test'), isNull);
      });

      test('requiredText should create composite validator', () {
        final validator = ValidationHelper.requiredText(
          minLength: 3,
          maxLength: 10,
        );

        expect(validator.validate(null), isNotNull);
        expect(validator.validate('ab'), isNotNull);
        expect(validator.validate('testing too long'), isNotNull);
        expect(validator.validate('test'), isNull);
      });

      test('requiredEmail should validate email', () {
        final validator = ValidationHelper.requiredEmail();

        expect(validator.validate(null), isNotNull);
        expect(validator.validate(''), isNotNull);
        expect(validator.validate('invalid'), isNotNull);
        expect(validator.validate('<EMAIL>'), isNull);
      });

      test('requiredNumber should validate numbers', () {
        final validator = ValidationHelper.requiredNumber(
          minValue: 0,
          maxValue: 100,
        );

        expect(validator.validate(null), isNotNull);
        expect(validator.validate(''), isNotNull);
        expect(validator.validate('abc'), isNotNull);
        expect(validator.validate('-5'), isNotNull);
        expect(validator.validate('150'), isNotNull);
        expect(validator.validate('50'), isNull);
      });
    });
  });
}
