import 'package:tijari_tech/data/local/database_constants.dart';

import '../local/dao/product_dao.dart';
import '../models/product.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class ProductRepository {
  final ProductDao _productDao = ProductDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء منتج جديد
  Future<String> createProduct(Product product) async {
    try {
      await _validateProduct(product);
      if (product.barcode != null && product.barcode!.isNotEmpty) {
        final exists = await _productDao.barcodeExists(product.barcode!);
        if (exists) throw ValidationException('Barcode already exists');
      }
      return await _productDao.insert(product);
    } catch (e) {
      AppUtils.logError('Error creating product', e);
      rethrow;
    }
  }

  /// استرجاع جميع المنتجات
  Future<List<Product>> getAllProducts({
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _productDao.findAll(
        orderBy: orderBy ?? DatabaseConstants.columnProductNameAr,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error getting all products', e);
      throw RepositoryException('Failed to get products');
    }
  }

  /// استرجاع منتج حسب ID
  Future<Product?> getProductById(String id) async {
    try {
      return await _productDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting product by ID', e);
      return null;
    }
  }

  /// استرجاع منتج حسب الباركود
  Future<Product?> getProductByBarcode(String barcode) async {
    try {
      return await _productDao.findByBarcode(barcode);
    } catch (e) {
      AppUtils.logError('Error getting product by barcode', e);
      return null;
    }
  }

  /// البحث عن المنتجات
  Future<List<Product>> searchProducts(String searchTerm) async {
    try {
      return searchTerm.trim().isEmpty
          ? await getAllProducts()
          : await _productDao.searchByName(searchTerm);
    } catch (e) {
      AppUtils.logError('Error searching products', e);
      return [];
    }
  }

  /// استرجاع المنتجات حسب الفئة
  Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      return await _productDao.findByCategory(categoryId);
    } catch (e) {
      AppUtils.logError('Error getting products by category', e);
      return [];
    }
  }

  /// استرجاع المنتجات النشطة فقط
  Future<List<Product>> getActiveProducts() async {
    try {
      return await _productDao.getActiveProducts();
    } catch (e) {
      AppUtils.logError('Error getting active products', e);
      return [];
    }
  }

  /// المنتجات مع معلومات المخزون
  Future<List<Map<String, dynamic>>> getProductsWithStock({
    String? warehouseId,
  }) async {
    try {
      return await _productDao.getProductsWithStock(warehouseId: warehouseId);
    } catch (e) {
      AppUtils.logError('Error getting products with stock', e);
      return [];
    }
  }

  /// المنتجات ذات المخزون المنخفض
  Future<List<Map<String, dynamic>>> getProductsWithLowStock() async {
    try {
      return await _productDao.getProductsWithLowStock();
    } catch (e) {
      AppUtils.logError('Error getting products with low stock', e);
      return [];
    }
  }

  /// أكثر المنتجات مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingProducts({
    int limit = 10,
  }) async {
    try {
      return await _productDao.getTopSellingProducts(limit: limit);
    } catch (e) {
      AppUtils.logError('Error getting top selling products', e);
      return [];
    }
  }

  /// المنتجات الراكدة
  Future<List<Map<String, dynamic>>> getSlowMovingProducts({
    int daysThreshold = 30,
  }) async {
    try {
      return await _productDao.getSlowMovingProducts(
          daysThreshold: daysThreshold);
    } catch (e) {
      AppUtils.logError('Error getting slow moving products', e);
      return [];
    }
  }

  /// إحصاءات المبيعات الخاصة بمنتج
  Future<Map<String, dynamic>?> getProductSalesStats(String productId) async {
    try {
      return await _productDao.getProductSalesStats(productId);
    } catch (e) {
      AppUtils.logError('Error getting product sales stats', e);
      return null;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(String id, Product product) async {
    try {
      await _validateProduct(product, excludeId: id);
      if (product.barcode != null && product.barcode!.isNotEmpty) {
        final exists = await _productDao.barcodeExists(
          product.barcode!,
          excludeProductId: id,
        );
        if (exists) throw ValidationException('Barcode already exists');
      }
      return await _productDao.update(id, product);
    } catch (e) {
      AppUtils.logError('Error updating product', e);
      rethrow;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(String id) async {
    try {
      final hasTransactions = await _hasTransactionHistory(id);
      if (hasTransactions) {
        throw ValidationException('Cannot delete product with transaction history');
      }
      return await _productDao.delete(id);
    } catch (e) {
      AppUtils.logError('Error deleting product', e);
      rethrow;
    }
  }

  /// المخزون الحالي
  Future<double> getCurrentStock(String productId, String warehouseId) async {
    try {
      return await _productDao.getCurrentStock(productId, warehouseId);
    } catch (e) {
      AppUtils.logError('Error getting current stock', e);
      return 0.0;
    }
  }

  /// تحديث المخزون
  Future<bool> updateStock(
      String productId, String warehouseId, double quantity) async {
    try {
      return await _productDao.updateStock(productId, warehouseId, quantity);
    } catch (e) {
      AppUtils.logError('Error updating stock', e);
      return false;
    }
  }

  /// استرجاع المنتجات حسب نطاق السعر
  Future<List<Product>> getProductsByPriceRange(
      double minPrice, double maxPrice) async {
    try {
      return await _productDao.getProductsByPriceRange(minPrice, maxPrice);
    } catch (e) {
      AppUtils.logError('Error getting products by price range', e);
      return [];
    }
  }

  /// تحديث أسعار بالجملة
  Future<bool> bulkUpdatePrices(
      Map<String, Map<String, double>> priceUpdates) async {
    try {
      return await _productDao.bulkUpdatePrices(priceUpdates);
    } catch (e) {
      AppUtils.logError('Error bulk updating prices', e);
      return false;
    }
  }

  /// عدّاد المنتجات مع فلاتر
  Future<int> countProducts({String? categoryId, bool? isActive}) async {
    try {
      String? where;
      List<dynamic>? whereArgs;

      if (categoryId != null || isActive != null) {
        final conditions = <String>[];
        whereArgs = <dynamic>[];

        if (categoryId != null) {
          conditions.add('${DatabaseConstants.columnProductCategoryId} = ?');
          whereArgs.add(categoryId);
        }

        if (isActive != null) {
          conditions.add('${DatabaseConstants.columnProductIsActive} = ?');
          whereArgs.add(isActive ? 1 : 0);
        }

        where = conditions.join(' AND ');
      }

      return await _productDao.count(where: where, whereArgs: whereArgs);
    } catch (e) {
      AppUtils.logError('Error counting products', e);
      return 0;
    }
  }

  /// ملف اختيار (dropdown) يحتوي على الثوابت
  Future<List<Map<String, dynamic>>> getProductsForSelection({
    bool activeOnly = true,
  }) async {
    try {
      final products =
          activeOnly ? await getActiveProducts() : await getAllProducts();

      return products.map((product) => {
            DatabaseConstants.columnProductId: product.id,
            DatabaseConstants.columnProductNameAr: product.nameAr,
            DatabaseConstants.columnProductBarcode: product.barcode,
            DatabaseConstants.columnProductSellingPrice: product.sellingPrice,
            DatabaseConstants.columnProductCostPrice: product.costPrice,
          }).toList();
    } catch (e) {
      AppUtils.logError('Error getting products for selection', e);
      return [];
    }
  }

  /// تحليلات المنتجات
  Future<Map<String, dynamic>> getProductAnalytics() async {
    try {
      final totalProducts = await countProducts();
      final activeProducts = await countProducts(isActive: true);
      final lowStockProducts = await getProductsWithLowStock();
      final topSellingProducts = await getTopSellingProducts(limit: 5);

      return {
        'total_products': totalProducts,
        'active_products': activeProducts,
        'inactive_products': totalProducts - activeProducts,
        'low_stock_count': lowStockProducts.length,
        'top_selling': topSellingProducts,
      };
    } catch (e) {
      AppUtils.logError('Error getting product analytics', e);
      return {};
    }
  }

  // ------------------------------------------------------------------
  // Utilities
  // ------------------------------------------------------------------

  /// التحقق من سجل المعاملات
  Future<bool> _hasTransactionHistory(String productId) async {
    try {
      final saleItemsCount = await _productDao.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableSaleItems} 
        WHERE ${DatabaseConstants.columnSaleItemProductId} = ?
      ''', [productId]);

      if ((saleItemsCount.first['count'] as int) > 0) return true;

      final purchaseItemsCount = await _productDao.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tablePurchaseItems} 
        WHERE ${DatabaseConstants.columnPurchaseItemProductId} = ?
      ''', [productId]);

      if ((purchaseItemsCount.first['count'] as int) > 0) return true;

      final stockMovementsCount = await _productDao.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableStockMovements} 
        WHERE ${DatabaseConstants.columnStockMovementProductId} = ?
      ''', [productId]);

      return (stockMovementsCount.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking transaction history', e);
      return false;
    }
  }

  /// التحقق من صحة بيانات المنتج
  Future<void> _validateProduct(Product product, {String? excludeId}) async {
    if (product.nameAr.trim().isEmpty) {
      throw ValidationException('Product name in Arabic is required');
    }
    if (product.costPrice < 0) {
      throw ValidationException('Cost price cannot be negative');
    }
    if (product.sellingPrice < 0) {
      throw ValidationException('Selling price cannot be negative');
    }
    if (product.sellingPrice < product.costPrice) {
      throw ValidationException(
          'Selling price cannot be less than cost price');
    }
    if (product.minStock < 0) {
      throw ValidationException('Minimum stock cannot be negative');
    }
    if (product.maxStock > 0 && product.maxStock < product.minStock) {
      throw ValidationException(
          'Maximum stock cannot be less than minimum stock');
    }
  }

  /// توليد باركود
  String generateBarcode() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return timestamp.substring(timestamp.length - 10);
  }

  /// التحقق من صيغة الباركود
  bool isValidBarcode(String barcode) {
    return barcode.isNotEmpty &&
        barcode.length >= 8 &&
        barcode.length <= 13 &&
        RegExp(r'^\d+$').hasMatch(barcode);
  }
}