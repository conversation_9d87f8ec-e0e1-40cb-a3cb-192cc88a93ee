import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../router/routes.dart';
import '../../providers/product_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class ProductsPage extends ConsumerStatefulWidget {
  const ProductsPage({super.key});

  @override
  ConsumerState<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends ConsumerState<ProductsPage> {
  final _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(productManagementProvider);

    return MainLayout(
      title: 'المنتجات',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.productAdd),
        child: const Icon(Icons.add),
      ),
      child: Column(
        children: [
          // Search and filters
          _buildSearchAndFilters(context, ref),
          
          // Products list
          Expanded(
            child: _buildProductsList(context, ref, productState),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن منتج...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        ref.read(productManagementProvider.notifier).searchProducts('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            onChanged: (value) {
              ref.read(productManagementProvider.notifier).searchProducts(value);
            },
          ),
          SizedBox(height: 16.h),
          
          // Filter chips
          ResponsiveRow(
            children: [
              _buildFilterChip(
                context,
                ref,
                label: 'جميع المنتجات',
                isSelected: ref.watch(productManagementProvider).selectedCategoryId == null,
                onTap: () {
                  ref.read(productManagementProvider.notifier).filterByCategory(null);
                },
              ),
              SizedBox(width: 8.w),
              _buildFilterChip(
                context,
                ref,
                label: 'مخزون منخفض',
                isSelected: false,
                onTap: () {
                  // TODO: Filter by low stock
                },
              ),
              SizedBox(width: 8.w),
              _buildFilterChip(
                context,
                ref,
                label: 'نفد المخزون',
                isSelected: false,
                onTap: () {
                  // TODO: Filter by out of stock
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    WidgetRef ref, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Widget _buildProductsList(BuildContext context, WidgetRef ref, ProductManagementState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.r,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ',
              style: AppTextStyles.titleMedium,
            ),
            SizedBox(height: 8.h),
            Text(
              state.error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                ref.read(productManagementProvider.notifier).refresh();
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state.filteredProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              state.searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد منتجات',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.searchQuery.isNotEmpty 
                  ? 'جرب البحث بكلمات أخرى'
                  : 'ابدأ بإضافة منتج جديد',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
            ),
            if (state.searchQuery.isEmpty) ...[
              SizedBox(height: 16.h),
              ElevatedButton(
                onPressed: () => context.go(AppRoutes.productAdd),
                child: const Text('إضافة منتج'),
              ),
            ],
          ],
        ),
      );
    }

    return ResponsiveBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1024) {
          return _buildProductsGrid(context, ref, state);
        } else {
          return _buildProductsList2(context, ref, state);
        }
      },
    );
  }

  Widget _buildProductsGrid(BuildContext context, WidgetRef ref, ProductManagementState state) {
    return GridView.builder(
      padding: EdgeInsets.all(16.r),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: 0.8,
      ),
      itemCount: state.filteredProducts.length,
      itemBuilder: (context, index) {
        final product = state.filteredProducts[index];
        return _buildProductCard(context, ref, product, index);
      },
    );
  }

  Widget _buildProductsList2(BuildContext context, WidgetRef ref, ProductManagementState state) {
    return ListView.separated(
      padding: EdgeInsets.all(16.r),
      itemCount: state.filteredProducts.length,
      separatorBuilder: (context, index) => SizedBox(height: 8.h),
      itemBuilder: (context, index) {
        final product = state.filteredProducts[index];
        return _buildProductListItem(context, ref, product, index);
      },
    );
  }

  Widget _buildProductCard(BuildContext context, WidgetRef ref, product, int index) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // TODO: Navigate to product details
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(12.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image placeholder
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.inventory_2_outlined,
                    size: 40.r,
                    color: Colors.grey[400],
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              
              // Product name
              Text(
                'منتج رقم ${index + 1}', // TODO: Use actual product name
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              
              // Product price
              Text(
                '${(index + 1) * 25.0} ر.س', // TODO: Use actual product price
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              
              // Stock status
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: index % 3 == 0 ? AppColors.success : AppColors.warning,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  index % 3 == 0 ? 'متوفر' : 'منخفض',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductListItem(BuildContext context, WidgetRef ref, product, int index) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Icon(
            Icons.inventory_2_outlined,
            color: AppColors.primary,
            size: 20.r,
          ),
        ),
        title: Text(
          'منتج رقم ${index + 1}', // TODO: Use actual product name
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          'الكمية: ${(index + 1) * 10} - السعر: ${(index + 1) * 25.0} ر.س', // TODO: Use actual data
          style: AppTextStyles.bodySmall,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: index % 3 == 0 ? AppColors.success : AppColors.warning,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                index % 3 == 0 ? 'متوفر' : 'منخفض',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    // TODO: Navigate to edit product
                    break;
                  case 'delete':
                    _showDeleteConfirmation(context, ref, index);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit_outlined),
                    title: Text('تعديل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete_outline, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          // TODO: Navigate to product details
        },
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف منتج رقم ${index + 1}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Delete product
              Navigator.of(context).pop();
              context.showSuccessSnackBar('تم حذف المنتج بنجاح');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
