// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_adjustment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StockAdjustment _$StockAdjustmentFromJson(Map<String, dynamic> json) =>
    StockAdjustment(
      id: json['id'] as String,
      referenceNumber: json['referenceNumber'] as String,
      adjustmentDate: DateTime.parse(json['adjustmentDate'] as String),
      warehouseId: json['warehouseId'] as String,
      reason: json['reason'] as String,
      notes: json['notes'] as String?,
      status: json['status'] as String? ?? 'draft',
      totalValue: (json['totalValue'] as num?)?.toDouble() ?? 0.0,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      branchId: json['branchId'] as String?,
      items: (json['items'] as List<dynamic>?)
              ?.map((e) =>
                  StockAdjustmentItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$StockAdjustmentToJson(StockAdjustment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'referenceNumber': instance.referenceNumber,
      'adjustmentDate': instance.adjustmentDate.toIso8601String(),
      'warehouseId': instance.warehouseId,
      'reason': instance.reason,
      'notes': instance.notes,
      'status': instance.status,
      'totalValue': instance.totalValue,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'branchId': instance.branchId,
      'items': instance.items,
    };

StockAdjustmentItem _$StockAdjustmentItemFromJson(Map<String, dynamic> json) =>
    StockAdjustmentItem(
      id: json['id'] as String,
      adjustmentId: json['adjustmentId'] as String,
      productId: json['productId'] as String,
      currentQuantity: (json['currentQuantity'] as num).toDouble(),
      adjustmentQuantity: (json['adjustmentQuantity'] as num).toDouble(),
      newQuantity: (json['newQuantity'] as num).toDouble(),
      adjustmentType: json['adjustmentType'] as String,
      unitCost: (json['unitCost'] as num).toDouble(),
      totalValue: (json['totalValue'] as num).toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$StockAdjustmentItemToJson(
        StockAdjustmentItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'adjustmentId': instance.adjustmentId,
      'productId': instance.productId,
      'currentQuantity': instance.currentQuantity,
      'adjustmentQuantity': instance.adjustmentQuantity,
      'newQuantity': instance.newQuantity,
      'adjustmentType': instance.adjustmentType,
      'unitCost': instance.unitCost,
      'totalValue': instance.totalValue,
      'notes': instance.notes,
    };
