import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'notification.g.dart';

@JsonSerializable()
class NotificationModel {
  final String id;
  final String? userId;
  final String title;
  final String body;
  final String type; // info, warning, error, success
  final bool isRead;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? readAt;

  const NotificationModel({
    required this.id,
    this.userId,
    required this.title,
    required this.body,
    this.type = 'info',
    this.isRead = false,
    this.metadata,
    required this.createdAt,
    this.readAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map[DatabaseConstants.columnNotificationId] as String,
      userId: map[DatabaseConstants.columnNotificationUserId] as String?,
      title: map[DatabaseConstants.columnNotificationTitle] as String,
      body: map[DatabaseConstants.columnNotificationBody] as String,
      type: map[DatabaseConstants.columnNotificationType] as String? ?? 'info',
      isRead: (map[DatabaseConstants.columnNotificationIsRead] as int) == 1,
      metadata: map[DatabaseConstants.columnNotificationMetadata] != null
          ? Map<String, dynamic>.from(
              map[DatabaseConstants.columnNotificationMetadata] as Map)
          : null,
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnNotificationCreatedAt] as String),
      readAt: map[DatabaseConstants.columnNotificationReadAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnNotificationReadAt] as String)
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnNotificationId: id,
      DatabaseConstants.columnNotificationUserId: userId,
      DatabaseConstants.columnNotificationTitle: title,
      DatabaseConstants.columnNotificationBody: body,
      DatabaseConstants.columnNotificationType: type,
      DatabaseConstants.columnNotificationIsRead: isRead ? 1 : 0,
      DatabaseConstants.columnNotificationMetadata: metadata,
      DatabaseConstants.columnNotificationCreatedAt:
          createdAt.toIso8601String(),
      DatabaseConstants.columnNotificationReadAt: readAt?.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? type,
    bool? isRead,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead)';
  }

  // Helper methods
  String get displayType {
    switch (type) {
      case NotificationType.info:
        return 'معلومات';
      case NotificationType.warning:
        return 'تحذير';
      case NotificationType.error:
        return 'خطأ';
      case NotificationType.success:
        return 'نجح';
      default:
        return type;
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

// Notification types constants
class NotificationType {
  static const String info = 'info';
  static const String warning = 'warning';
  static const String error = 'error';
  static const String success = 'success';

  static List<String> get allTypes => [
        info,
        warning,
        error,
        success,
      ];
}
