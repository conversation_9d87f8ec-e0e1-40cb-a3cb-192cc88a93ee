import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class InventoryPage extends ConsumerWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'المخزون',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.productAdd),
        child: const Icon(Icons.add),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            _buildQuickStats(context),
            SizedBox(height: 24.h),

            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),

            // Recent Products
            _buildRecentProducts(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات المخزون',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          children: [
            _buildStatCard(
              context,
              title: 'إجمالي المنتجات',
              value: '1,234',
              icon: Icons.inventory_2_outlined,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'مخزون منخفض',
              value: '23',
              icon: Icons.warning_amber_outlined,
              color: AppColors.warning,
            ),
            _buildStatCard(
              context,
              title: 'نفد المخزون',
              value: '5',
              icon: Icons.error_outline,
              color: AppColors.error,
            ),
            _buildStatCard(
              context,
              title: 'قيمة المخزون',
              value: '125,0 ر.س',
              icon: Icons.attach_money,
              color: AppColors.success,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildActionCard(
              context,
              title: 'المنتجات',
              subtitle: 'عرض وإدارة المنتجات',
              icon: Icons.inventory_2_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.products),
            ),
            _buildActionCard(
              context,
              title: 'التصنيفات',
              subtitle: 'إدارة تصنيفات المنتجات',
              icon: Icons.category_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.categories),
            ),
            _buildActionCard(
              context,
              title: 'وحدات القياس',
              subtitle: 'إدارة وحدات القياس',
              icon: Icons.straighten_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.units),
            ),
            _buildActionCard(
              context,
              title: 'سجل المخزون',
              subtitle: 'عرض حركات المخزون',
              icon: Icons.history_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.stockLog),
            ),
            _buildActionCard(
              context,
              title: 'تعديل المخزون',
              subtitle: 'تعديل كميات المخزون',
              icon: Icons.edit_outlined,
              color: AppColors.warning,
              onTap: () => context.go(AppRoutes.stockAdjustment),
            ),
            _buildActionCard(
              context,
              title: 'إضافة منتج',
              subtitle: 'إضافة منتج جديد',
              icon: Icons.add_box_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.productAdd),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentProducts(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المنتجات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.products),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.inventory_2_outlined,
                    color: AppColors.primary,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  'منتج رقم ${index + 1}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'الكمية: ${(index + 1) * 10} - السعر: ${(index + 1) * 25.0} ر.س',
                  style: AppTextStyles.bodySmall,
                ),
                trailing: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: index < 2 ? AppColors.success : AppColors.warning,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    index < 2 ? 'متوفر' : 'منخفض',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                onTap: () {
                  // TODO: Navigate to product details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
