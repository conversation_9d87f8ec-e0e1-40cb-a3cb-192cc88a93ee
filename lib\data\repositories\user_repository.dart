import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/data/local/database_constants.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

import '../local/database.dart';
import '../models/user.dart';

class UserRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// استرجاع جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      orderBy: '${DatabaseConstants.columnUserCreatedAt} DESC',
    );
    return maps.map((map) => User.fromMap(map)).toList();
  }

  /// استرجاع مستخدم حسب ID
  Future<User?> getUserById(String id) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [id],
    );
    return maps.isNotEmpty ? User.fromMap(maps.first) : null;
  }

  /// استرجاع مستخدم حسب البريد الإلكتروني
  Future<User?> getUserByEmail(String email) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserEmail} = ?',
      whereArgs: [email],
    );
    return maps.isNotEmpty ? User.fromMap(maps.first) : null;
  }

  /// إنشاء مستخدم جديد
  Future<String> createUser(User user) async {
    final sql.Database db = await _databaseHelper.database;
    final String id = user.id.isEmpty ? _uuid.v4() : user.id;

    final User newUser = user.copyWith(
      id: id,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await db.insert(DatabaseConstants.tableUsers, newUser.toMap());
    return id;
  }

  /// تحديث مستخدم
  Future<void> updateUser(User user) async {
    final sql.Database db = await _databaseHelper.database;
    final User updatedUser = user.copyWith(updatedAt: DateTime.now());

    await db.update(
      DatabaseConstants.tableUsers,
      updatedUser.toMap(),
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [user.id],
    );
  }

  /// حذف مستخدم
  Future<void> deleteUser(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.delete(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [id],
    );
  }

  /// تفعيل/تعطيل مستخدم
  Future<void> toggleUserStatus(String id, bool isActive) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableUsers,
      {
        DatabaseConstants.columnUserIsActive: isActive ? 1 : 0,
        DatabaseConstants.columnUserUpdatedAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [id],
    );
  }

  /// تحديث آخر دخول
  Future<void> updateLastLogin(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableUsers,
      {
        DatabaseConstants.columnUserLastLoginAt: DateTime.now().toIso8601String(),
        DatabaseConstants.columnUserUpdatedAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [id],
    );
  }

  /// تسجيل دخول
  Future<User?> authenticateUser(String email, String password) async {
    final User? user = await getUserByEmail(email);
    if (user != null && user.isActive) {
      if (user.passwordHash == password) {
        await updateLastLogin(user.id);
        return user;
      }
    }
    return null;
  }

  /// تغيير كلمة المرور
  Future<void> changePassword(String userId, String newPassword) async {
    final sql.Database db = await _databaseHelper.database;
    final String hashedPassword = _hashPassword(newPassword);

    await db.update(
      DatabaseConstants.tableUsers,
      {
        DatabaseConstants.columnUserPasswordHash: hashedPassword,
        DatabaseConstants.columnUserUpdatedAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [userId],
    );
  }

  /// تحديث الصلاحيات
  Future<void> updateUserPermissions(String userId, List<String> permissions) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableUsers,
      {
        DatabaseConstants.columnUserPermissions: permissions.join(','),
        DatabaseConstants.columnUserUpdatedAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnUserId} = ?',
      whereArgs: [userId],
    );
  }

  /// استرجاع المستخدمين حسب الدور
  Future<List<User>> getUsersByRole(String role) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserRole} = ? AND ${DatabaseConstants.columnUserIsActive} = 1',
      whereArgs: [role],
      orderBy: DatabaseConstants.columnUserName,
    );
    return maps.map((map) => User.fromMap(map)).toList();
  }

  /// استرجاع المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserIsActive} = 1',
      orderBy: DatabaseConstants.columnUserName,
    );
    return maps.map((map) => User.fromMap(map)).toList();
  }

  /// البحث عن المستخدمين
  Future<List<User>> searchUsers(String query) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableUsers,
      where: '${DatabaseConstants.columnUserName} LIKE ? OR ${DatabaseConstants.columnUserEmail} LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: DatabaseConstants.columnUserName,
    );
    return maps.map((map) => User.fromMap(map)).toList();
  }

  /// عدّاد المستخدمين حسب الدور
  Future<Map<String, int>> getUsersCountByRole() async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnUserRole}, 
             COUNT(*) as count 
      FROM ${DatabaseConstants.tableUsers} 
      WHERE ${DatabaseConstants.columnUserIsActive} = 1 
      GROUP BY ${DatabaseConstants.columnUserRole}
    ''');

    final Map<String, int> counts = {};
    for (final row in result) {
      counts[row[DatabaseConstants.columnUserRole] as String] =
          row['count'] as int;
    }
    return counts;
  }

  /// إحصاءات المستخدمين
  Future<Map<String, dynamic>> getUsersStatistics() async {
    final sql.Database db = await _databaseHelper.database;

    final List<Map<String, dynamic>> totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${DatabaseConstants.tableUsers}');

    final List<Map<String, dynamic>> activeResult = await db.rawQuery(
        'SELECT COUNT(*) as active FROM ${DatabaseConstants.tableUsers} WHERE ${DatabaseConstants.columnUserIsActive} = 1');

    final List<Map<String, dynamic>> recentResult = await db.rawQuery(
      'SELECT COUNT(*) as recent FROM ${DatabaseConstants.tableUsers} '
      'WHERE ${DatabaseConstants.columnUserCreatedAt} > ?',
      [DateTime.now().subtract(const Duration(days: 30)).toIso8601String()],
    );

    return {
      'total': totalResult.first['total'] as int,
      'active': activeResult.first['active'] as int,
      'inactive': (totalResult.first['total'] as int) -
          (activeResult.first['active'] as int),
      'recent': recentResult.first['recent'] as int,
    };
  }

  // ------------------------------------------------------------------
  // Utilities
  // ------------------------------------------------------------------

  /// تصدير بيانات المستخدمين
  Future<List<Map<String, dynamic>>> exportUsers() async {
    final sql.Database db = await _databaseHelper.database;
    return await db.query(
      DatabaseConstants.tableUsers,
      orderBy: DatabaseConstants.columnUserCreatedAt,
    );
  }

  /// استيراد بيانات المستخدمين
  Future<void> importUsers(List<Map<String, dynamic>> usersData) async {
    final sql.Database db = await _databaseHelper.database;
    final sql.Batch batch = db.batch();

    for (final userData in usersData) {
      if (userData[DatabaseConstants.columnUserId] == null ||
          userData[DatabaseConstants.columnUserId].isEmpty) {
        userData[DatabaseConstants.columnUserId] = _uuid.v4();
      }

      userData[DatabaseConstants.columnUserCreatedAt] =
          DateTime.now().toIso8601String();
      userData[DatabaseConstants.columnUserUpdatedAt] =
          DateTime.now().toIso8601String();

      batch.insert(
        DatabaseConstants.tableUsers,
        userData,
        conflictAlgorithm: sql.ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  /// تنظيف المستخدمين غير النشطين
  Future<int> cleanupInactiveUsers(int daysOld) async {
    final sql.Database db = await _databaseHelper.database;
    final DateTime cutoffDate =
        DateTime.now().subtract(Duration(days: daysOld));

    return await db.delete(
      DatabaseConstants.tableUsers,
      where:
          '${DatabaseConstants.columnUserIsActive} = 0 AND ${DatabaseConstants.columnUserUpdatedAt} < ?',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  /// تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من كلمة المرور
  bool _verifyPassword(String password, String hashedPassword) {
    return _hashPassword(password) == hashedPassword;
  }
}