import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'customer.g.dart';

@JsonSerializable()
class Customer {
  final String id;
  final String name;
  final String? phone;
  final String? email;
  final double balance;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Customer({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.balance = 0.0,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new Customer instance from a map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map[DatabaseConstants.columnCustomerId] as String,
      name: map[DatabaseConstants.columnCustomerName] as String,
      phone: map[DatabaseConstants.columnCustomerPhone] as String?,
      email: map[DatabaseConstants.columnCustomerEmail] as String?,
      balance:
          (map[DatabaseConstants.columnCustomerBalance] as num?)?.toDouble() ??
              0.0,
      notes: map['notes'] as String?,
      createdAt: map[DatabaseConstants.columnCustomerCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCustomerCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnCustomerUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCustomerUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnCustomerDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnCustomerDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnCustomerIsSynced] as int? ?? 0) == 1,
    );
  }

  // Convert Customer instance to a map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnCustomerId: id,
      DatabaseConstants.columnCustomerName: name,
      DatabaseConstants.columnCustomerPhone: phone,
      DatabaseConstants.columnCustomerEmail: email,
      DatabaseConstants.columnCustomerBalance: balance,
      'notes': notes,
      DatabaseConstants.columnCustomerCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnCustomerUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnCustomerDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnCustomerIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  // Copy with method for creating modified copies
  Customer copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    double? balance,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Customer(id: $id, name: $name, phone: $phone, balance: $balance, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasDebt => balance > 0;
  bool get hasCredit => balance < 0;
  bool get isBalanced => balance == 0;

  // Get balance status
  BalanceStatus get balanceStatus {
    if (balance > 0) return BalanceStatus.debt;
    if (balance < 0) return BalanceStatus.credit;
    return BalanceStatus.balanced;
  }

  // Get absolute balance
  double get absoluteBalance => balance.abs();

  // Create a new customer with current timestamp
  static Customer create({
    required String id,
    required String name,
    String? phone,
    String? email,
    double balance = 0.0,
    String? notes,
  }) {
    final now = DateTime.now();
    return Customer(
      id: id,
      name: name,
      phone: phone,
      email: email,
      balance: balance,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update customer with new timestamp
  Customer update({
    String? name,
    String? phone,
    String? email,
    double? balance,
    String? notes,
  }) {
    return copyWith(
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Add to balance (increase debt or decrease credit)
  Customer addToBalance(double amount) {
    return copyWith(
      balance: balance + amount,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Subtract from balance (decrease debt or increase credit)
  Customer subtractFromBalance(double amount) {
    return copyWith(
      balance: balance - amount,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Set balance to zero
  Customer clearBalance() {
    return copyWith(
      balance: 0.0,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Customer markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Customer markAsSynced() {
    return copyWith(isSynced: true);
  }

  // Remove phone
  Customer removePhone() {
    return copyWith(
      phone: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Remove email
  Customer removeEmail() {
    return copyWith(
      email: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Remove notes
  Customer removeNotes() {
    return copyWith(
      notes: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }
}

// Balance status enum
enum BalanceStatus {
  debt, // العميل مدين (له رصيد)
  credit, // العميل دائن (عليه رصيد)
  balanced, // متوازن
}

// Extension for balance status
extension BalanceStatusExtension on BalanceStatus {
  String get displayName {
    switch (this) {
      case BalanceStatus.debt:
        return 'مدين';
      case BalanceStatus.credit:
        return 'دائن';
      case BalanceStatus.balanced:
        return 'متوازن';
    }
  }

  String get displayNameEn {
    switch (this) {
      case BalanceStatus.debt:
        return 'Debt';
      case BalanceStatus.credit:
        return 'Credit';
      case BalanceStatus.balanced:
        return 'Balanced';
    }
  }
}
