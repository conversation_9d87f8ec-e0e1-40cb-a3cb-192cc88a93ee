import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/data/local/database_constants.dart';
import 'package:uuid/uuid.dart';

import '../local/database.dart';
import '../models/notification.dart';

class NotificationRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Uuid _uuid = const Uuid();

  // Get all notifications
  Future<List<NotificationModel>> getAllNotifications({
    int? limit,
    int? offset,
    String? userId,
    bool? isRead,
  }) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '${DatabaseConstants.columnNotificationDeletedAt} IS NULL';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause += ' AND ${DatabaseConstants.columnNotificationUserId} = ?';
      whereArgs.add(userId);
    }

    if (isRead != null) {
      whereClause += ' AND ${DatabaseConstants.columnNotificationIsRead} = ?';
      whereArgs.add(isRead ? 1 : 0);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableNotifications,
      where: whereClause,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: '${DatabaseConstants.columnNotificationCreatedAt} DESC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => NotificationModel.fromMap(map)).toList();
  }

  // Get notification by ID
  Future<NotificationModel?> getNotificationById(String id) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableNotifications,
      where: '${DatabaseConstants.columnNotificationId} = ? AND ${DatabaseConstants.columnNotificationDeletedAt} IS NULL',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return NotificationModel.fromMap(maps.first);
    }
    return null;
  }

  // Create notification
  Future<String> createNotification(NotificationModel notification) async {
    final sql.Database db = await _databaseHelper.database;
    final String id = notification.id.isEmpty ? _uuid.v4() : notification.id;

    final NotificationModel newNotification = notification.copyWith(
      id: id,
      createdAt: DateTime.now(),
    );

    await db.insert(DatabaseConstants.tableNotifications, newNotification.toMap());
    return id;
  }

  // Update notification
  Future<void> updateNotification(NotificationModel notification) async {
    final sql.Database db = await _databaseHelper.database;

    await db.update(
      DatabaseConstants.tableNotifications,
      notification.toMap(),
      where: '${DatabaseConstants.columnNotificationId} = ?',
      whereArgs: [notification.id],
    );
  }

  // Delete notification (soft delete)
  Future<void> deleteNotification(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableNotifications,
      {
        DatabaseConstants.columnNotificationDeletedAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnNotificationId} = ?',
      whereArgs: [id],
    );
  }

  // Mark notification as read
  Future<void> markAsRead(String id) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableNotifications,
      {
        DatabaseConstants.columnNotificationIsRead: 1,
        DatabaseConstants.columnNotificationReadAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnNotificationId} = ?',
      whereArgs: [id],
    );
  }

  // Mark all notifications as read for user
  Future<void> markAllAsReadForUser(String userId) async {
    final sql.Database db = await _databaseHelper.database;
    await db.update(
      DatabaseConstants.tableNotifications,
      {
        DatabaseConstants.columnNotificationIsRead: 1,
        DatabaseConstants.columnNotificationReadAt: DateTime.now().toIso8601String(),
      },
      where: '${DatabaseConstants.columnNotificationUserId} = ? AND ${DatabaseConstants.columnNotificationIsRead} = 0 AND ${DatabaseConstants.columnNotificationDeletedAt} IS NULL',
      whereArgs: [userId],
    );
  }

  // Get unread notifications count
  Future<int> getUnreadCount({String? userId}) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '${DatabaseConstants.columnNotificationIsRead} = 0 AND ${DatabaseConstants.columnNotificationDeletedAt} IS NULL';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause += ' AND ${DatabaseConstants.columnNotificationUserId} = ?';
      whereArgs.add(userId);
    }

    final List<Map<String, dynamic>> result = await db.query(
      DatabaseConstants.tableNotifications,
      columns: ['COUNT(*) as count'],
      where: whereClause,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
    );

    return result.first['count'] as int;
  }

  // Get notifications by type
  Future<List<NotificationModel>> getNotificationsByType(String type) async {
    final sql.Database db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableNotifications,
      where: '${DatabaseConstants.columnNotificationType} = ? AND ${DatabaseConstants.columnNotificationDeletedAt} IS NULL',
      whereArgs: [type],
      orderBy: '${DatabaseConstants.columnNotificationCreatedAt} DESC',
    );
    return maps.map((map) => NotificationModel.fromMap(map)).toList();
  }

  // Get recent notifications
  Future<List<NotificationModel>> getRecentNotifications({
    String? userId,
    int limit = 10,
  }) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '${DatabaseConstants.columnNotificationDeletedAt} IS NULL';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause += ' AND ${DatabaseConstants.columnNotificationUserId} = ?';
      whereArgs.add(userId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseConstants.tableNotifications,
      where: whereClause,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: '${DatabaseConstants.columnNotificationCreatedAt} DESC',
      limit: limit,
    );

    return maps.map((map) => NotificationModel.fromMap(map)).toList();
  }

  // Clean old notifications
  Future<int> cleanOldNotifications(int daysOld) async {
    final sql.Database db = await _databaseHelper.database;
    final DateTime cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

    return await db.delete(
      DatabaseConstants.tableNotifications,
      where: '${DatabaseConstants.columnNotificationCreatedAt} < ? OR ${DatabaseConstants.columnNotificationDeletedAt} IS NOT NULL',
      whereArgs: [cutoffDate.toIso8601String()],
    );
  }

  // Get notification statistics
  Future<Map<String, dynamic>> getNotificationStatistics({String? userId}) async {
    final sql.Database db = await _databaseHelper.database;

    String whereClause = '${DatabaseConstants.columnNotificationDeletedAt} IS NULL';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause += ' AND ${DatabaseConstants.columnNotificationUserId} = ?';
      whereArgs.add(userId);
    }

    // Total notifications
    final List<Map<String, dynamic>> totalResult = await db.query(
      DatabaseConstants.tableNotifications,
      columns: ['COUNT(*) as total'],
      where: whereClause,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
    );

    // Unread notifications
    final List<Map<String, dynamic>> unreadResult = await db.query(
      DatabaseConstants.tableNotifications,
      columns: ['COUNT(*) as unread'],
      where: '$whereClause AND ${DatabaseConstants.columnNotificationIsRead} = 0',
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
    );

    // Notifications by type
    final List<Map<String, dynamic>> byTypeResult = await db.rawQuery('''
      SELECT ${DatabaseConstants.columnNotificationType}, COUNT(*) as count 
      FROM ${DatabaseConstants.tableNotifications} 
      WHERE $whereClause
      GROUP BY ${DatabaseConstants.columnNotificationType}
    ''', whereArgs.isNotEmpty ? whereArgs : null);

    final Map<String, int> byType = {};
    for (final row in byTypeResult) {
      byType[row[DatabaseConstants.columnNotificationType] as String] = row['count'] as int;
    }

    return {
      'total': totalResult.first['total'] as int,
      'unread': unreadResult.first['unread'] as int,
      'by_type': byType,
    };
  }

  // Batch operations
  Future<void> createMultipleNotifications(List<NotificationModel> notifications) async {
    final sql.Database db = await _databaseHelper.database;
    final sql.Batch batch = db.batch();

    for (final notification in notifications) {
      final String id = notification.id.isEmpty ? _uuid.v4() : notification.id;
      final NotificationModel newNotification = notification.copyWith(
        id: id,
        createdAt: DateTime.now(),
      );
      batch.insert(DatabaseConstants.tableNotifications, newNotification.toMap());
    }

    await batch.commit();
  }

  // Send notification to user
  Future<String> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    String type = 'info',
    Map<String, dynamic>? metadata,
  }) async {
    final notification = NotificationModel(
      id: _uuid.v4(),
      userId: userId,
      title: title,
      body: body,
      type: type,
      metadata: metadata,
      createdAt: DateTime.now(),
    );

    return await createNotification(notification);
  }

  // Send notification to all users
  Future<void> sendNotificationToAllUsers({
    required String title,
    required String body,
    String type = 'info',
    Map<String, dynamic>? metadata,
  }) async {
    final notification = NotificationModel(
      id: _uuid.v4(),
      userId: null, // null means all users
      title: title,
      body: body,
      type: type,
      metadata: metadata,
      createdAt: DateTime.now(),
    );

    await createNotification(notification);
  }
  
}