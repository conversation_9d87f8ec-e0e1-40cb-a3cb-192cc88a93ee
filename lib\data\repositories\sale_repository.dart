import 'package:tijari_tech/data/local/database_constants.dart';

import '../local/dao/sale_dao.dart';
import '../models/sale.dart';
import '../models/sale_item.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class SaleRepository {
  final SaleDao _saleDao = SaleDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء فاتورة بيع مع عناصرها
  Future<String> createSale(Sale sale, List<SaleItem> items) async {
    try {
      await _validateSale(sale, items);
      await _validateStockAvailability(items);
      return await _saleDao.createSaleWithItems(sale, items);
    } catch (e) {
      AppUtils.logError('Error creating sale', e);
      rethrow;
    }
  }

  /// استرجاع جميع المبيعات
  Future<List<Map<String, dynamic>>> getAllSales({
    int? limit,
    int? offset,
    String? orderBy,
    bool ascending = false,
  }) async {
    try {
      final sales = await _saleDao.findAll(
        orderBy: orderBy ?? DatabaseConstants.columnSaleSaleDate,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );

      final result = <Map<String, dynamic>>[];
      for (final sale in sales) {
        final saleMap = sale.toMap();

        // إضافة اسم العميل إن وُجد
        if (sale.customerId != null) {
          final customerInfo = await _getCustomerInfo(sale.customerId!);
          saleMap['customer_name'] = customerInfo?['name'];
        }

        // عدّاد العناصر
        final itemCount = await _getSaleItemCount(sale.id);
        saleMap['item_count'] = itemCount;

        result.add(saleMap);
      }
      return result;
    } catch (e) {
      AppUtils.logError('Error getting all sales', e);
      return [];
    }
  }

  /// استرجاع فاتورة بكامل تفاصيلها
  Future<Map<String, dynamic>?> getSaleById(String id) async {
    try {
      return await _saleDao.getSaleWithItems(id);
    } catch (e) {
      AppUtils.logError('Error getting sale by ID', e);
      return null;
    }
  }

  /// استرجاع المبيعات حسب الفترة الزمنية
  Future<List<Map<String, dynamic>>> getSalesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _saleDao.getSalesByDateRange(startDate, endDate);
    } catch (e) {
      AppUtils.logError('Error getting sales by date range', e);
      return [];
    }
  }

  /// استرجاع مبيعات عميل معين
  Future<List<Map<String, dynamic>>> getSalesByCustomer(
      String customerId) async {
    try {
      return await _saleDao.getSalesByCustomer(customerId);
    } catch (e) {
      AppUtils.logError('Error getting sales by customer', e);
      return [];
    }
  }

  /// مبيعات اليوم
  Future<List<Map<String, dynamic>>> getTodaysSales() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      return await getSalesByDateRange(startOfDay, endOfDay);
    } catch (e) {
      AppUtils.logError('Error getting today\'s sales', e);
      return [];
    }
  }

  /// ملخص المبيعات اليومية
  Future<List<Map<String, dynamic>>> getDailySalesSummary({
    int days = 30,
  }) async {
    try {
      return await _saleDao.getDailySalesSummary(days: days);
    } catch (e) {
      AppUtils.logError('Error getting daily sales summary', e);
      return [];
    }
  }

  /// أفضل العملاء حسب حجم المبيعات
  Future<List<Map<String, dynamic>>> getTopCustomers({
    int limit = 10,
  }) async {
    try {
      return await _saleDao.getTopCustomersBySales(limit: limit);
    } catch (e) {
      AppUtils.logError('Error getting top customers', e);
      return [];
    }
  }

  /// الفواتير المستحقة
  Future<List<Map<String, dynamic>>> getPendingPayments() async {
    try {
      return await _saleDao.getPendingPayments();
    } catch (e) {
      AppUtils.logError('Error getting pending payments', e);
      return [];
    }
  }

  /// تحديث الدفعة
  Future<bool> updatePayment(String saleId, double paidAmount) async {
    try {
      if (paidAmount <= 0) {
        throw ValidationException('Payment amount must be greater than zero');
      }
      return await _saleDao.updatePayment(saleId, paidAmount);
    } catch (e) {
      AppUtils.logError('Error updating sale payment', e);
      rethrow;
    }
  }

  /// إلغاء فاتورة
  Future<bool> cancelSale(String saleId, String reason) async {
    try {
      if (reason.trim().isEmpty) {
        throw ValidationException('Cancellation reason is required');
      }
      return await _saleDao.cancelSale(saleId, reason);
    } catch (e) {
      AppUtils.logError('Error cancelling sale', e);
      rethrow;
    }
  }

  /// حذف فاتورة
  Future<bool> deleteSale(String saleId) async {
    try {
      return await _saleDao.delete(saleId);
    } catch (e) {
      AppUtils.logError('Error deleting sale', e);
      rethrow;
    }
  }

  /// إحصاءات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();
      final sales = await getSalesByDateRange(start, end);

      double totalAmount = 0;
      double totalPaid = 0;
      double totalDue = 0;
      int totalSales = sales.length;

      for (final sale in sales) {
        totalAmount +=
            (sale[DatabaseConstants.columnSaleTotalAmount] as num).toDouble();
        totalPaid +=
            (sale[DatabaseConstants.columnSalePaidAmount] as num).toDouble();
        totalDue +=
            (sale[DatabaseConstants.columnSaleDueAmount] as num).toDouble();
      }

      return {
        'total_sales': totalSales,
        'total_amount': totalAmount,
        'total_paid': totalPaid,
        'total_due': totalDue,
        'average_sale': totalSales > 0 ? totalAmount / totalSales : 0,
        'payment_rate': totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0,
      };
    } catch (e) {
      AppUtils.logError('Error getting sales statistics', e);
      return {};
    }
  }

  /// توليد رقم فاتورة
  Future<String> generateInvoiceNumber() async {
    try {
      final today = DateTime.now();
      final datePrefix =
          '${today.year}${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';
      final todaysSales = await getTodaysSales();
      final sequence = (todaysSales.length + 1).toString().padLeft(3, '0');
      return 'INV-$datePrefix-$sequence';
    } catch (e) {
      AppUtils.logError('Error generating invoice number', e);
      return 'INV-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// البحث عن الفواتير
  Future<List<Map<String, dynamic>>> searchSales(String searchTerm) async {
    try {
      if (searchTerm.trim().isEmpty) return await getAllSales(limit: 50);

      final sql = '''
        SELECT 
          s.*,
          c.${DatabaseConstants.columnCustomerName} as customer_name,
          COUNT(si.${DatabaseConstants.columnSaleItemId}) as item_count
        FROM ${DatabaseConstants.tableSales} s
        LEFT JOIN ${DatabaseConstants.tableCustomers} c 
          ON s.${DatabaseConstants.columnSaleCustomerId} = c.${DatabaseConstants.columnCustomerId}
        LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
          ON s.${DatabaseConstants.columnSaleId} = si.${DatabaseConstants.columnSaleItemSaleId}
        WHERE s.${DatabaseConstants.columnSaleDeletedAt} IS NULL 
          AND (
            s.${DatabaseConstants.columnSaleInvoiceNo} LIKE ? OR 
            c.${DatabaseConstants.columnCustomerName} LIKE ?
          )
        GROUP BY s.${DatabaseConstants.columnSaleId}
        ORDER BY s.${DatabaseConstants.columnSaleSaleDate} DESC
        LIMIT 50
      ''';

      return await _saleDao.rawQuery(sql, ['%$searchTerm%', '%$searchTerm%']);
    } catch (e) {
      AppUtils.logError('Error searching sales', e);
      return [];
    }
  }

  /// تحليلات المبيعات
  Future<Map<String, dynamic>> getSalesAnalytics() async {
    try {
      final today = DateTime.now();
      final startOfMonth = DateTime(today.year, today.month, 1);

      final todaysSales = await getTodaysSales();
      final todaysTotal = todaysSales.fold<double>(
        0,
        (sum, sale) =>
            sum +
            (sale[DatabaseConstants.columnSaleTotalAmount] as num).toDouble(),
      );

      final monthSales = await getSalesByDateRange(startOfMonth, today);
      final monthTotal = monthSales.fold<double>(
        0,
        (sum, sale) =>
            sum +
            (sale[DatabaseConstants.columnSaleTotalAmount] as num).toDouble(),
      );

      final pendingPayments = await getPendingPayments();
      final totalDue = pendingPayments.fold<double>(
        0,
        (sum, sale) =>
            sum +
            (sale[DatabaseConstants.columnSaleDueAmount] as num).toDouble(),
      );

      return {
        'todays_sales_count': todaysSales.length,
        'todays_sales_amount': todaysTotal,
        'month_sales_count': monthSales.length,
        'month_sales_amount': monthTotal,
        'pending_payments_count': pendingPayments.length,
        'total_due_amount': totalDue,
      };
    } catch (e) {
      AppUtils.logError('Error getting sales analytics', e);
      return {};
    }
  }

  // ------------------------------------------------------------------
  // Private Helpers
  // ------------------------------------------------------------------

  /// التحقق من صحة الفاتورة
  Future<void> _validateSale(Sale sale, List<SaleItem> items) async {
    if (items.isEmpty) {
      throw ValidationException('Sale must have at least one item');
    }
    if (sale.total <= 0) {
      throw ValidationException('Sale total amount must be greater than zero');
    }
    if (sale.paid < 0) {
      throw ValidationException('Paid amount cannot be negative');
    }
    if (sale.paid > sale.total) {
      throw ValidationException('Paid amount cannot exceed total amount');
    }

    for (final item in items) {
      if (item.qty <= 0) {
        throw ValidationException('Item quantity must be greater than zero');
      }
      if (item.unitPrice <= 0) {
        throw ValidationException('Item unit price must be greater than zero');
      }
    }
  }

  /// التحقق من توافر المخزون
  Future<void> _validateStockAvailability(List<SaleItem> items) async {
    for (final item in items) {
      final currentStock = await _saleDao.rawQuery('''
        SELECT ${DatabaseConstants.columnStockQuantity} 
        FROM ${DatabaseConstants.tableStocks} 
        WHERE ${DatabaseConstants.columnStockProductId} = ?
        LIMIT 1
      ''', [item.productId]);

      if (currentStock.isNotEmpty) {
        final availableQty =
            (currentStock.first[DatabaseConstants.columnStockQuantity] as num)
                .toDouble();
        if (item.qty > availableQty) {
          final productInfo = await _getProductInfo(item.productId);
          final productName =
              productInfo?[DatabaseConstants.columnProductNameAr] ??
                  'Unknown Product';
          throw ValidationException(
              'Insufficient stock for $productName. Available: $availableQty, Required: ${item.qty}');
        }
      }
    }
  }

  /// استرجاع معلومات العميل
  Future<Map<String, dynamic>?> _getCustomerInfo(String customerId) async {
    try {
      final result = await _saleDao.rawQuery('''
        SELECT ${DatabaseConstants.columnCustomerName} 
        FROM ${DatabaseConstants.tableCustomers} 
        WHERE ${DatabaseConstants.columnCustomerId} = ? 
          AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL
      ''', [customerId]);
      return result.isNotEmpty ? result.first : null;
    } catch (e) {
      return null;
    }
  }

  /// استرجاع معلومات المنتج
  Future<Map<String, dynamic>?> _getProductInfo(String productId) async {
    try {
      final result = await _saleDao.rawQuery('''
        SELECT ${DatabaseConstants.columnProductNameAr} 
        FROM ${DatabaseConstants.tableProducts} 
        WHERE ${DatabaseConstants.columnProductId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''', [productId]);
      return result.isNotEmpty ? result.first : null;
    } catch (e) {
      return null;
    }
  }

  /// عدّاد عناصر الفاتورة
  Future<int> _getSaleItemCount(String saleId) async {
    try {
      final result = await _saleDao.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableSaleItems} 
        WHERE ${DatabaseConstants.columnSaleItemSaleId} = ?
      ''', [saleId]);
      return result.isNotEmpty ? (result.first['count'] as int) : 0;
    } catch (e) {
      return 0;
    }
  }
}
