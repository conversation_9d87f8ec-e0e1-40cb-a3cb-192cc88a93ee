class AppConstants {
  // App Information
  static const String appName = 'تجاري تك';
  static const String appNameEn = 'Tijari Tech';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'نظام محاسبي وإداري شامل';

  // Database
  static const String databaseName = 'tijari_tech.db';
  static const int databaseVersion = 2; // تم تحديث الإصدار للجداول الجديدة

  // API Configuration
  static const String baseUrl = 'https://api.tijaritech.com';
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int maxNotifications = 20;

  // Currency
  static const String defaultCurrency = 'ر.س';
  static const String defaultCurrencyCode = 'SAR';
  static const int currencyDecimalPlaces = 2;

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String arabicDateFormat = 'dd MMMM yyyy';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;
  static const int maxNotesLength = 1000;

  // File Sizes (in bytes)
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDocumentSize = 10 * 1024 * 1024; // 10MB

  // Sync Configuration
  static const Duration syncInterval = Duration(minutes: 15);
  static const int maxSyncRetries = 3;
  static const Duration syncRetryDelay = Duration(seconds: 30);

  // Cache Configuration
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardElevation = 2.0;
  static const double modalElevation = 8.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Business Rules
  static const double defaultTaxRate = 0.15; // 15% VAT
  static const int invoiceNumberLength = 8;
  static const String invoicePrefix = 'INV';
  static const String purchasePrefix = 'PUR';
  static const String receiptPrefix = 'REC';
  static const String paymentPrefix = 'PAY';

  // Inventory
  static const double defaultReorderPoint = 10.0;
  static const double minStockLevel = 0.0;
  static const int barcodeLength = 13; // EAN-13

  // Permissions
  static const List<String> adminPermissions = [
    'view_all',
    'create_all',
    'edit_all',
    'delete_all',
    'manage_users',
    'manage_settings',
    'view_reports',
    'backup_restore',
  ];

  static const List<String> managerPermissions = [
    'view_all',
    'create_all',
    'edit_all',
    'view_reports',
  ];

  static const List<String> cashierPermissions = [
    'view_products',
    'create_sales',
    'view_sales',
    'create_receipts',
  ];

  // Report Types
  static const List<String> reportTypes = [
    'sales_report',
    'purchase_report',
    'inventory_report',
    'customer_report',
    'supplier_report',
    'cash_flow_report',
    'profit_loss_report',
    'tax_report',
  ];

  // Transaction Types
  static const String transactionTypeIn = 'in';
  static const String transactionTypeOut = 'out';

  // Stock Movement Types
  static const String stockMovementIn = 'IN';
  static const String stockMovementOut = 'OUT';
  static const String stockMovementAdjust = 'ADJUST';
  static const String stockMovementReturn = 'RETURN';
  static const String stockMovementTransfer = 'TRANSFER';

  // Payment Methods
  static const List<String> paymentMethods = [
    'cash',
    'card',
    'bank_transfer',
    'check',
    'credit',
  ];

  // Invoice Status
  static const String invoiceStatusDraft = 'draft';
  static const String invoiceStatusPending = 'pending';
  static const String invoiceStatusPaid = 'paid';
  static const String invoiceStatusPartiallyPaid = 'partially_paid';
  static const String invoiceStatusOverdue = 'overdue';
  static const String invoiceStatusCancelled = 'cancelled';

  // Employee Status
  static const String employeeStatusActive = 'active';
  static const String employeeStatusInactive = 'inactive';
  static const String employeeStatusSuspended = 'suspended';

  // Backup Configuration
  static const String backupFileExtension = '.tijari';
  static const Duration autoBackupInterval = Duration(days: 1);
  static const int maxBackupFiles = 10;

  // Printer Configuration
  static const int thermalPrinterWidth = 48; // characters
  static const int a4PrinterWidth = 80; // characters
  static const String defaultPrinterEncoding = 'utf-8';

  // Localization
  static const String defaultLocale = 'ar';
  static const List<String> supportedLocales = ['ar', 'en'];

  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String validationErrorMessage =
      'يرجى التحقق من البيانات المدخلة';
  static const String permissionErrorMessage =
      'ليس لديك صلاحية للقيام بهذا الإجراء';

  // Success Messages
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String syncSuccessMessage = 'تم التزامن بنجاح';

  // Shared Preferences Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyUserId = 'user_id';
  static const String keyUserName = 'user_name';
  static const String keyBranchId = 'branch_id';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyAutoSync = 'auto_sync';
  static const String keyPrinterSettings = 'printer_settings';
  static const String keyTaxRate = 'tax_rate';
  static const String keyCompanyInfo = 'company_info';

  // Regular Expressions
  static const String emailRegex =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^[0-9]{10,15}$';
  static const String barcodeRegex = r'^[0-9]{8,13}$';
  static const String invoiceNumberRegex = r'^[A-Z]{3}[0-9]{8}$';

  // Default Values
  static const String defaultCustomerName = 'عميل نقدي';
  static const String defaultSupplierName = 'مورد عام';
  static const String defaultCashBoxName = 'الصندوق الرئيسي';
  static const String defaultBranchName = 'الفرع الرئيسي';
  static const String defaultCategoryName = 'عام';
  static const String defaultUnitName = 'قطعة';
  static const String defaultWarehouseName = 'المخزن الرئيسي';
  static const String defaultAdminUserName = 'مدير النظام';
  static const String defaultAdminEmail = '<EMAIL>';

  // Company Information
  static const String defaultCompanyName = 'شركة تجاري تك المحدودة';
  static const String defaultCompanyAddress =
      'الرياض، المملكة العربية السعودية';
  static const String defaultCompanyPhone = '0112345678';
  static const String defaultCompanyEmail = '<EMAIL>';
}
