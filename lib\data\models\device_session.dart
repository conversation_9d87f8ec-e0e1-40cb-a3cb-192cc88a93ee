import 'package:json_annotation/json_annotation.dart';
import '../local/database_constants.dart';

part 'device_session.g.dart';

@JsonSerializable()
class DeviceSession {
  final String id;
  final String userId;
  final String? deviceName;
  final String? deviceOs;
  final String? appVersion;
  final String? ipAddress;
  final String? token;
  final bool isActive;
  final DateTime? lastLoginAt;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const DeviceSession({
    required this.id,
    required this.userId,
    this.deviceName,
    this.deviceOs,
    this.appVersion,
    this.ipAddress,
    this.token,
    this.isActive = true,
    this.lastLoginAt,
    required this.createdAt,
    this.updatedAt,
  });

  factory DeviceSession.fromJson(Map<String, dynamic> json) =>
      _$DeviceSessionFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceSessionToJson(this);

  factory DeviceSession.fromMap(Map<String, dynamic> map) {
    return DeviceSession(
      id: map[DatabaseConstants.columnDeviceSessionId] as String,
      userId: map[DatabaseConstants.columnDeviceSessionUserId] as String,
      deviceName:
          map[DatabaseConstants.columnDeviceSessionDeviceName] as String?,
      deviceOs: map[DatabaseConstants.columnDeviceSessionDeviceOs] as String?,
      appVersion:
          map[DatabaseConstants.columnDeviceSessionAppVersion] as String?,
      ipAddress: map[DatabaseConstants.columnDeviceSessionIpAddress] as String?,
      token: map[DatabaseConstants.columnDeviceSessionToken] as String?,
      isActive:
          (map[DatabaseConstants.columnDeviceSessionIsActive] as int) == 1,
      lastLoginAt: map[DatabaseConstants.columnDeviceSessionLastLoginAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnDeviceSessionLastLoginAt] as String)
          : null,
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnDeviceSessionCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnDeviceSessionUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnDeviceSessionUpdatedAt] as String)
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnDeviceSessionId: id,
      DatabaseConstants.columnDeviceSessionUserId: userId,
      DatabaseConstants.columnDeviceSessionDeviceName: deviceName,
      DatabaseConstants.columnDeviceSessionDeviceOs: deviceOs,
      DatabaseConstants.columnDeviceSessionAppVersion: appVersion,
      DatabaseConstants.columnDeviceSessionIpAddress: ipAddress,
      DatabaseConstants.columnDeviceSessionToken: token,
      DatabaseConstants.columnDeviceSessionIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnDeviceSessionLastLoginAt:
          lastLoginAt?.toIso8601String(),
      DatabaseConstants.columnDeviceSessionCreatedAt:
          createdAt.toIso8601String(),
      DatabaseConstants.columnDeviceSessionUpdatedAt:
          updatedAt?.toIso8601String(),
    };
  }

  DeviceSession copyWith({
    String? id,
    String? userId,
    String? deviceName,
    String? deviceOs,
    String? appVersion,
    String? ipAddress,
    String? token,
    bool? isActive,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DeviceSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      deviceName: deviceName ?? this.deviceName,
      deviceOs: deviceOs ?? this.deviceOs,
      appVersion: appVersion ?? this.appVersion,
      ipAddress: ipAddress ?? this.ipAddress,
      token: token ?? this.token,
      isActive: isActive ?? this.isActive,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DeviceSession(id: $id, userId: $userId, deviceName: $deviceName, isActive: $isActive)';
  }

  // Helper methods
  String get displayDeviceName {
    if (deviceName != null && deviceName!.isNotEmpty) {
      return deviceName!;
    }
    return 'جهاز غير معروف';
  }

  String get displayOs {
    if (deviceOs != null && deviceOs!.isNotEmpty) {
      return deviceOs!;
    }
    return 'نظام تشغيل غير معروف';
  }

  String get lastLoginDisplay {
    if (lastLoginAt == null) return 'لم يسجل دخول';

    final now = DateTime.now();
    final difference = now.difference(lastLoginAt!);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String get statusDisplay {
    return isActive ? 'نشط' : 'غير نشط';
  }
}
