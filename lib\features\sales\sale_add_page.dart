import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class SaleAddPage extends ConsumerStatefulWidget {
  const SaleAddPage({super.key});

  @override
  ConsumerState<SaleAddPage> createState() => _SaleAddPageState();
}

class _SaleAddPageState extends ConsumerState<SaleAddPage> {
  final _invoiceNumberController = TextEditingController();
  final _customerSearchController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String _paymentMethod = 'نقداً';
  String _selectedBranch = 'المخزن الرئيسي';
  String _selectedWarehouse = 'الكل';
  bool _isDetailedView = true;

  final List<Map<String, dynamic>> _invoiceItems = [
    {
      'name': 'شامبو أوليفا 4 باكت * شده 12 * ...',
      'barcode': '200000025',
      'quantity': 25,
      'price': 0.0,
      'total': 0.0,
    },
    {
      'name': 'شامبو أوليفا 4 باكت * شده 12 * ...',
      'barcode': '200000025',
      'quantity': 25,
      'price': 0.0,
      'total': 0.0,
    },
    {
      'name': 'صابونة جسم زيان كبير عدود 12 شده ...',
      'barcode': '200000028',
      'quantity': 28,
      'price': 0.0,
      'total': 0.0,
    },
  ];

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _customerSearchController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: '',
      // backgroundColor: AppColors.primary,
      actions: [
        // Top action buttons
        Row(
          children: [
            _buildTopActionButton('حفظ وطباعة', Icons.print, Colors.orange),
            SizedBox(width: 8.w),
            _buildTopActionButton('حفظ', Icons.save, Colors.green),
            SizedBox(width: 8.w),
            _buildTopActionButton('خصم', Icons.percent, Colors.red),
            SizedBox(width: 16.w),
          ],
        ),
      ],
      child: Column(
        children: [
          // Header section with invoice details
          _buildHeaderSection(),

          // Customer and warehouse section
          _buildCustomerSection(),

          // Items table
          Expanded(
            child: _buildItemsTable(),
          ),

          // Summary section
          _buildSummarySection(),
        ],
      ),
    );
  }

  Widget _buildTopActionButton(String text, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16.r),
          SizedBox(width: 4.w),
          Text(
            text,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Invoice number and date row
          Row(
            children: [
              // Invoice number
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الرقم',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today,
                              size: 16.r, color: Colors.green),
                          SizedBox(width: 8.w),
                          Text(
                            '156',
                            style: AppTextStyles.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),

              // Date
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '14/05/2025',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Icon(Icons.calendar_today,
                            size: 16.r, color: Colors.green),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '1',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Payment method row
          Row(
            children: [
              // Cash/Credit buttons
              Row(
                children: [
                  _buildPaymentButton('آجل', _paymentMethod == 'آجل'),
                  SizedBox(width: 8.w),
                  _buildPaymentButton('نقداً', _paymentMethod == 'نقداً'),
                ],
              ),
              const Spacer(),
              Text(
                'العملة',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // Currency dropdown
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: 'ريال',
                isExpanded: true,
                items: ['ريال', 'دولار', 'يورو'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle currency change
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentButton(String text, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _paymentMethod = text;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey[200],
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[400]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected ? Colors.white : Colors.grey[600],
              size: 16.r,
            ),
            SizedBox(width: 4.w),
            Text(
              text,
              style: AppTextStyles.bodySmall.copyWith(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Container(
      color: Colors.grey[50],
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Reference number and commissions
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رقم المرجع',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text('0'),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, color: Colors.white, size: 16.r),
                    SizedBox(width: 4.w),
                    Text(
                      'عمولات',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Customer search and warehouse selection
          Row(
            children: [
              // Customer search
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الحساب ** اضغط هنا للبحث',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.red,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: TextField(
                        controller: _customerSearchController,
                        decoration: InputDecoration(
                          hintText: 'البحث',
                          suffixIcon: const Icon(Icons.search),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 8.h),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),

              // Warehouse selection
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المخزن',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warehouse, size: 16.r),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedWarehouse,
                                isExpanded: true,
                                items: [
                                  'الكل',
                                  'المخزن الرئيسي',
                                  'المخزن الفرعي'
                                ].map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(value),
                                  );
                                }).toList(),
                                onChanged: (String? newValue) {
                                  setState(() {
                                    _selectedWarehouse = newValue!;
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Balance and customer details
          Row(
            children: [
              Text(
                'الرصيد عليكم',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '367,204,190.90',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // Customer name fields
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اسم العميل النقدي',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: const Text(''),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اسم العميل الآجل',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: const Text(''),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Notes section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'البيان/الملاحظة',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: TextField(
                  controller: _notesController,
                  maxLines: 2,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'أدخل الملاحظات هنا...',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTable() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Items header with add button
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.inventory, color: Colors.white, size: 16.r),
                      SizedBox(width: 4.w),
                      Text(
                        'قائمة الأصناف',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.add, color: Colors.white, size: 16.r),
                      SizedBox(width: 4.w),
                      Text(
                        'إضافة صنف',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.qr_code_scanner,
                          color: Colors.white, size: 16.r),
                      SizedBox(width: 4.w),
                      Text(
                        'الباركود',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Table header
          Container(
            color: AppColors.primary,
            child: Row(
              children: [
                _buildTableHeaderCell('رقم الصنف', flex: 2),
                _buildTableHeaderCell('الباركود', flex: 2),
                _buildTableHeaderCell('الكمية', flex: 1),
                _buildTableHeaderCell('التصنيف', flex: 1),
              ],
            ),
          ),

          // Table rows
          Expanded(
            child: ListView.builder(
              itemCount: _invoiceItems.length,
              itemBuilder: (context, index) {
                final item = _invoiceItems[index];
                final isHighlighted = index % 2 == 1; // Alternate highlighting

                return Container(
                  color: isHighlighted ? Colors.yellow[100] : Colors.white,
                  child: Row(
                    children: [
                      _buildTableCell(
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['name'],
                              style: AppTextStyles.bodySmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4.h),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.edit,
                                      color: Colors.green, size: 16.r),
                                  onPressed: () {
                                    // Handle edit
                                  },
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete,
                                      color: Colors.red, size: 16.r),
                                  onPressed: () {
                                    // Handle delete
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                        flex: 2,
                      ),
                      _buildTableCell(
                        Text(
                          item['barcode'].toString(),
                          style: AppTextStyles.bodySmall,
                        ),
                        flex: 2,
                      ),
                      _buildTableCell(
                        Text(
                          item['quantity'].toString(),
                          style: AppTextStyles.bodySmall.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        flex: 1,
                      ),
                      _buildTableCell(
                        IconButton(
                          icon:
                              Icon(Icons.edit, color: Colors.green, size: 16.r),
                          onPressed: () {
                            // Handle classification edit
                          },
                        ),
                        flex: 1,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeaderCell(String text, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
        child: Text(
          text,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildTableCell(Widget child, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: child,
      ),
    );
  }

  Widget _buildSummarySection() {
    return Container(
      color: Colors.grey[900],
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Summary row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryItem('الضريبة', '0.0', Colors.red),
              _buildSummaryItem('الخصم', '520000', Colors.orange),
              _buildSummaryItem('عدد القطع', '6', Colors.blue),
              _buildSummaryItem('إجمالي الفاتورة', '1,620', Colors.green),
              _buildSummaryItem('صافي الفاتورة', '518,380-', Colors.white),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
