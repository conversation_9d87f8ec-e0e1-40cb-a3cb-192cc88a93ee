import 'package:tijari_tech/data/local/database_constants.dart';
import 'base_dao.dart';
import '../../models/category.dart';
import '../../../core/utils/app_utils.dart';

class CategoryDao extends BaseDao<Category> {
  @override
  String get tableName => DatabaseConstants.tableCategories;

  @override
  Category fromMap(Map<String, dynamic> map) => Category.fromMap(map);

  @override
  Map<String, dynamic> toMap(Category entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Category-specific queries
  // ------------------------------------------------------------------

  /// استرجاع الفئات الجذرية (بدون أب)
  Future<List<Category>> getRootCategories() async {
    return await findWhere(
      where: '${DatabaseConstants.columnCategoryParentId} IS NULL',
      orderBy: DatabaseConstants.columnCategoryNameAr,
    );
  }

  /// استرجاع الفئات الفرعية لفئة معينة
  Future<List<Category>> getSubcategories(String parentId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnCategoryParentId} = ?',
      whereArgs: [parentId],
      orderBy: DatabaseConstants.columnCategoryNameAr,
    );
  }

  /// استرجاع هرم الفئات (الأب وجميع الأبناء)
  Future<List<Map<String, dynamic>>> getCategoryHierarchy() async {
    final sql = '''
      WITH RECURSIVE category_tree AS (
        -- الحالة الأساسية: الفئات الجذرية
        SELECT 
          ${DatabaseConstants.columnCategoryId}, 
          ${DatabaseConstants.columnCategoryNameAr}, 
          ${DatabaseConstants.columnCategoryNameEn}, 
          ${DatabaseConstants.columnCategoryParentId}, 
          0 as level,
          ${DatabaseConstants.columnCategoryNameAr} as path
        FROM ${DatabaseConstants.tableCategories} 
        WHERE ${DatabaseConstants.columnCategoryParentId} IS NULL 
          AND ${DatabaseConstants.columnCategoryDeletedAt} IS NULL
        
        UNION ALL
        
        -- الحالة التكرارية: الفئات الفرعية
        SELECT 
          c.${DatabaseConstants.columnCategoryId}, 
          c.${DatabaseConstants.columnCategoryNameAr}, 
          c.${DatabaseConstants.columnCategoryNameEn}, 
          c.${DatabaseConstants.columnCategoryParentId},
          ct.level + 1,
          ct.path || ' > ' || c.${DatabaseConstants.columnCategoryNameAr}
        FROM ${DatabaseConstants.tableCategories} c
        JOIN category_tree ct ON c.${DatabaseConstants.columnCategoryParentId} = ct.${DatabaseConstants.columnCategoryId}
        WHERE c.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      )
      SELECT * FROM category_tree
      ORDER BY path
    ''';

    return await rawQuery(sql);
  }

  /// البحث عن الفئات حسب الاسم
  Future<List<Category>> searchByName(String searchTerm) async {
    return await findWhere(
      where:
          '${DatabaseConstants.columnCategoryNameAr} LIKE ? OR ${DatabaseConstants.columnCategoryNameEn} LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: DatabaseConstants.columnCategoryNameAr,
    );
  }

  /// استرجاع الفئات مع عدّاد المنتجات
  Future<List<Map<String, dynamic>>> getCategoriesWithProductCount() async {
    final sql = '''
      SELECT 
        c.*,
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count
      FROM ${DatabaseConstants.tableCategories} c
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON c.${DatabaseConstants.columnCategoryId} = p.${DatabaseConstants.columnProductCategoryId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      WHERE c.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      GROUP BY c.${DatabaseConstants.columnCategoryId}, 
               c.${DatabaseConstants.columnCategoryNameAr}, 
               c.${DatabaseConstants.columnCategoryNameEn}, 
               c.${DatabaseConstants.columnCategoryParentId}, 
               c.${DatabaseConstants.columnCategoryCreatedAt}, 
               c.${DatabaseConstants.columnCategoryUpdatedAt}
      ORDER BY c.${DatabaseConstants.columnCategoryNameAr}
    ''';

    return await rawQuery(sql);
  }

  /// التحقق من وجود فئات فرعية
  Future<bool> hasSubcategories(String categoryId) async {
    final count = await this.count(
      where: '${DatabaseConstants.columnCategoryParentId} = ?',
      whereArgs: [categoryId],
    );
    return count > 0;
  }

  /// التحقق من وجود منتجات داخل الفئة
  Future<bool> hasProducts(String categoryId) async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProducts} 
        WHERE ${DatabaseConstants.columnProductCategoryId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''', [categoryId]);

      return (result.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking if category has products', e);
      return false;
    }
  }

  /// استرجاع مسار الفئة (breadcrumb)
  Future<List<Category>> getCategoryPath(String categoryId) async {
    final sql = '''
      WITH RECURSIVE category_path AS (
        -- البدء بالفئة المطلوبة
        SELECT 
          ${DatabaseConstants.columnCategoryId}, 
          ${DatabaseConstants.columnCategoryNameAr}, 
          ${DatabaseConstants.columnCategoryNameEn}, 
          ${DatabaseConstants.columnCategoryParentId}, 
          0 as level
        FROM ${DatabaseConstants.tableCategories} 
        WHERE ${DatabaseConstants.columnCategoryId} = ? 
          AND ${DatabaseConstants.columnCategoryDeletedAt} IS NULL
        
        UNION ALL
        
        -- استرجاع الفئات الأب
        SELECT c.${DatabaseConstants.columnCategoryId}, 
               c.${DatabaseConstants.columnCategoryNameAr}, 
               c.${DatabaseConstants.columnCategoryNameEn}, 
               c.${DatabaseConstants.columnCategoryParentId}, 
               cp.level + 1
        FROM ${DatabaseConstants.tableCategories} c
        JOIN category_path cp ON c.${DatabaseConstants.columnCategoryId} = cp.${DatabaseConstants.columnCategoryParentId}
        WHERE c.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      )
      SELECT * FROM category_path
      ORDER BY level DESC
    ''';

    final result = await rawQuery(sql, [categoryId]);
    return result.map((map) => Category.fromMap(map)).toList();
  }

  /// نقل الفئة إلى فئة أب مختلفة
  Future<bool> moveCategory(String categoryId, String? newParentId) async {
    try {
      if (newParentId != null) {
        final isCircular = await _wouldCreateCircularReference(categoryId, newParentId);
        if (isCircular) {
          throw Exception('Cannot move category: would create circular reference');
        }
      }

      final category = await findById(categoryId);
      if (category != null) {
        return await update(
          categoryId,
          category.copyWith(parentId: newParentId),
        );
      }
      return false;
    } catch (e) {
      AppUtils.logError('Error moving category', e);
      return false;
    }
  }

  /// التحقق من وجود دورة مرجعية
  Future<bool> _wouldCreateCircularReference(
      String categoryId, String newParentId) async {
    final path = await getCategoryPath(newParentId);
    return path.any((cat) => cat.id == categoryId);
  }

  /// استرجاع إحصاءات الفئة
  Future<Map<String, dynamic>?> getCategoryStatistics(String categoryId) async {
    final sql = '''
      SELECT 
        c.${DatabaseConstants.columnCategoryId},
        c.${DatabaseConstants.columnCategoryNameAr},
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT sc.${DatabaseConstants.columnCategoryId}) as subcategory_count,
        COALESCE(SUM(s.${DatabaseConstants.columnStockQuantity} * p.${DatabaseConstants.columnProductCostPrice}), 0) as total_stock_value,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}), 0) as total_revenue
      FROM ${DatabaseConstants.tableCategories} c
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON c.${DatabaseConstants.columnCategoryId} = p.${DatabaseConstants.columnProductCategoryId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableCategories} sc 
        ON c.${DatabaseConstants.columnCategoryId} = sc.${DatabaseConstants.columnCategoryParentId} 
       AND sc.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} sal 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = sal.${DatabaseConstants.columnSaleId}
      WHERE c.${DatabaseConstants.columnCategoryId} = ? 
        AND c.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      GROUP BY c.${DatabaseConstants.columnCategoryId}, c.${DatabaseConstants.columnCategoryNameAr}
    ''';

    final result = await rawQuery(sql, [categoryId]);
    return result.isNotEmpty ? result.first : null;
  }

  /// استرجاع أفضل الفئات حسب المبيعات
  Future<List<Map<String, dynamic>>> getTopCategoriesBySales(
      {int limit = 10}) async {
    final sql = '''
      SELECT 
        c.*,
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}), 0) as total_revenue
      FROM ${DatabaseConstants.tableCategories} c
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON c.${DatabaseConstants.columnCategoryId} = p.${DatabaseConstants.columnProductCategoryId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} s 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = s.${DatabaseConstants.columnSaleId}
      WHERE c.${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      GROUP BY c.${DatabaseConstants.columnCategoryId}
      ORDER BY total_revenue DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  /// حذف الفئة مع التعامل مع المنتجات
  Future<bool> deleteCategoryWithProducts(String categoryId,
      {String? moveProductsToCategory}) async {
    try {
      final db = await database;

      await db.transaction((txn) async {
        // نقل المنتجات إلى فئة أخرى أو تعيينها كـ null
        if (moveProductsToCategory != null) {
          await txn.update(
            DatabaseConstants.tableProducts,
            {
              DatabaseConstants.columnProductCategoryId: moveProductsToCategory,
              DatabaseConstants.columnProductUpdatedAt:
                  DateTime.now().toIso8601String(),
              DatabaseConstants.columnProductIsSynced: 0,
            },
            where:
                '${DatabaseConstants.columnProductCategoryId} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
            whereArgs: [categoryId],
          );
        } else {
          await txn.update(
            DatabaseConstants.tableProducts,
            {
              DatabaseConstants.columnProductCategoryId: null,
              DatabaseConstants.columnProductUpdatedAt:
                  DateTime.now().toIso8601String(),
              DatabaseConstants.columnProductIsSynced: 0,
            },
            where:
                '${DatabaseConstants.columnProductCategoryId} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
            whereArgs: [categoryId],
          );
        }

        // نقل الفئات الفرعية إلى الأب أو الجذر
        final category = await findById(categoryId);
        if (category != null) {
          await txn.update(
            DatabaseConstants.tableCategories,
            {
              DatabaseConstants.columnCategoryParentId: category.parentId,
              DatabaseConstants.columnCategoryUpdatedAt:
                  DateTime.now().toIso8601String(),
              DatabaseConstants.columnCategoryIsSynced: 0,
            },
            where:
                '${DatabaseConstants.columnCategoryParentId} = ? AND ${DatabaseConstants.columnCategoryDeletedAt} IS NULL',
            whereArgs: [categoryId],
          );
        }

        // حذف ناعم للفئة
        await txn.update(
          DatabaseConstants.tableCategories,
          {
            DatabaseConstants.columnCategoryDeletedAt:
                DateTime.now().toIso8601String(),
            DatabaseConstants.columnCategoryUpdatedAt:
                DateTime.now().toIso8601String(),
            DatabaseConstants.columnCategoryIsSynced: 0,
          },
          where: '${DatabaseConstants.columnCategoryId} = ?',
          whereArgs: [categoryId],
        );
      });

      AppUtils.logInfo('Deleted category with ID: $categoryId');
      return true;
    } catch (e) {
      AppUtils.logError('Error deleting category with products', e);
      return false;
    }
  }

  /// التحقق من تفرّد اسم الفئة
  Future<bool> isNameUnique(String nameAr, {String? excludeCategoryId}) async {
    try {
      String where =
          '${DatabaseConstants.columnCategoryNameAr} = ? AND ${DatabaseConstants.columnCategoryDeletedAt} IS NULL';
      List<dynamic> whereArgs = [nameAr];

      if (excludeCategoryId != null) {
        where += ' AND ${DatabaseConstants.columnCategoryId} != ?';
        whereArgs.add(excludeCategoryId);
      }

      final count = await this.count(where: where, whereArgs: whereArgs);
      return count == 0;
    } catch (e) {
      AppUtils.logError('Error checking category name uniqueness', e);
      return false;
    }
  }
}